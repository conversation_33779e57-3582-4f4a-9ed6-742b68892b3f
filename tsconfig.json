{
  "compilerOptions": {
    "lib": ["DOM", "ES2020"],
    "jsx": "react",
    "target": "ES2020",
    "noEmit": true,
    "skipLibCheck": true,
    "useDefineForClassFields": true,

    /* modules */
    "module": "ESNext",
    "resolveJsonModule": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,

    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,

    /* paths */
    "baseUrl": "./",
    "outDir": "./lib",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src"],
  "ts-node": {
    "compilerOptions": {
      "module": "CommonJS"
    }
  }
}
