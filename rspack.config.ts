import { defineConfig } from '@rspack/cli';
import { rspack } from '@rspack/core';
import { ReactRefreshRspackPlugin } from '@rspack/plugin-react-refresh';
import { resolve } from 'path';

const isDev = process.env.NODE_ENV === 'development';

// Target browsers, see: https://github.com/browserslist/browserslist
const targets = ['chrome >= 87', 'edge >= 88', 'firefox >= 78', 'safari >= 14'];

const loadConfig = () => {
  const env = process.env.NODE_ENV;
  const configPath = resolve(__dirname, `config/config.${env}.ts`);

  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require(configPath);
  } catch (error) {
    throw new Error(`Failed to load config file for environment '${env}', message: ${error}`);
  }
};

const config = loadConfig();

export default defineConfig({
  context: __dirname,
  entry: {
    Initialization: './src/initialization/index.ts',
    Editor: './src/editor/index.ts',
  },
  output: {
    path: resolve(__dirname, 'lib'),
    clean: true,
  },
  resolve: {
    extensions: ['...', '.ts', '.tsx', '.jsx'],
    alias: {
      '@': resolve(__dirname, 'src'),
    },
    tsConfig: resolve(__dirname, './tsconfig.json'),
  },
  devServer: {
    port: 8001,
    static: false,
  },
  devtool: false,
  module: {
    rules: [
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        use: ['@svgr/webpack'],
      },
      {
        test: /\.(tsx?|ts?)$/,
        use: [
          {
            loader: 'builtin:swc-loader',
            options: {
              jsc: {
                parser: {
                  syntax: 'typescript',
                  tsx: true,
                },
                transform: {
                  react: {
                    runtime: 'automatic',
                    development: isDev,
                    refresh: isDev,
                  },
                },
                experimental: {
                  plugins: [
                    [
                      '@swc/plugin-styled-components',
                      {
                        displayName: false,
                      },
                    ],
                  ],
                },
              },
              env: { targets },
            },
          },
        ],
      },
    ],
  },
  plugins: [
    isDev && new ReactRefreshRspackPlugin(),
    isDev && new rspack.HotModuleReplacementPlugin(),
    new rspack.DefinePlugin({
      'process.env.SDK_HOST': JSON.stringify(config.SDK_HOST),
      'process.env.API_HOST': JSON.stringify(config.API_HOST),
    }),
  ].filter(Boolean),
});
