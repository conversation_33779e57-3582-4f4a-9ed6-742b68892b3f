import CalendarLocale from 'rc-picker/lib/locale/pl_PL';

import TimePickerLocale from '../../time-picker/locale/pl_PL';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON><PERSON>rz datę',
    rangePlaceholder: ['Data początkowa', 'Data końcowa'],
    yearFormat: 'YYYY',
    dateFormat: 'M/D/YYYY',
    dayFormat: 'D',
    dateTimeFormat: 'M/D/YYYY HH:mm:ss',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    shortWeekDays: ['Niedz', 'Pon', 'Wt', 'Śr', 'Czw', 'Pt', 'Sob'],
    shortMonths: [
      'Sty',
      'Lut',
      'Mar',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
    ],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
