---
category: Components
title: Button
description: To trigger an operation.
cover: https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*7va7RKs3YzIAAAAAAAAAAAAADrJ8AQ/original
coverDark: https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*3T4cRqxH9-8AAAAAAAAAAAAADrJ8AQ/original
demo:
  cols: 2
group:
  title: General
  order: 1
---

## When To Use

A button means an operation (or a series of operations). Clicking a button will trigger its corresponding business logic.

In Ant Design we provide 5 types of button.

- Primary button: used for the main action, there can be at most one primary button in a section.
- Default button: used for a series of actions without priority.
- Dashed button: commonly used for adding more actions.
- Text button: used for the most secondary action.
- Link button: used for external links.

And 4 other properties additionally.

- `danger`: used for actions of risk, like deletion or authorization.
- `ghost`: used in situations with complex background, home pages usually.
- `disabled`: used when actions are not available.
- `loading`: adds a loading spinner in button, avoids multiple submits too.

## Examples

<!-- prettier-ignore -->
<code src="./demo/basic.tsx">Type</code>
<code src="./demo/icon.tsx">Icon</code>
<code src="./demo/icon-position.tsx" version="5.17.0">Icon Position</code>
<code src="./demo/debug-icon.tsx" debug>Debug Icon</code>
<code src="./demo/debug-block.tsx" debug>Debug Block</code>
<code src="./demo/size.tsx">Size</code>
<code src="./demo/disabled.tsx">Disabled</code>
<code src="./demo/loading.tsx">Loading</code>
<code src="./demo/multiple.tsx">Multiple Buttons</code>
<code src="./demo/ghost.tsx">Ghost Button</code>
<code src="./demo/danger.tsx">Danger Buttons</code>
<code src="./demo/block.tsx">Block Button</code>
<code src="./demo/legacy-group.tsx" debug>Deprecated Button Group</code>
<code src="./demo/chinese-chars-loading.tsx" debug>Loading style bug</code>
<code src="./demo/component-token.tsx" debug>Component Token</code>
<code src="./demo/linear-gradient.tsx">Gradient Button</code>

## API

Common props ref：[Common props](/docs/react/common-props)

Different button styles can be generated by setting Button properties. The recommended order is: `type` -> `shape` -> `size` -> `loading` -> `disabled`.

| Property | Description | Type | Default | Version |
| --- | --- | --- | --- | --- |
| autoInsertSpace | We add a space between two Chinese characters by default, which can be removed by setting `autoInsertSpace` to `false`. | boolean | `true` | 5.17.0 |
| block | Option to fit button width to its parent width | boolean | false |  |
| classNames | Semantic DOM class | [Record<SemanticDOM, string>](#semantic-dom) | - | 5.4.0 |
| danger | Set the danger status of button | boolean | false |  |
| disabled | Disabled state of button | boolean | false |  |
| ghost | Make background transparent and invert text and border colors | boolean | false |  |
| href | Redirect url of link button | string | - |  |
| htmlType | Set the original html `type` of `button`, see: [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/button#attr-type) | string | `button` |  |
| icon | Set the icon component of button | ReactNode | - |  |
| iconPosition | Set the icon position of button | `start` \| `end` | `start` | 5.17.0 |
| loading | Set the loading status of button | boolean \| { delay: number } | false |  |
| shape | Can be set button shape | `default` \| `circle` \| `round` | `default` |  |
| size | Set the size of button | `large` \| `middle` \| `small` | `middle` |  |
| styles | Semantic DOM style | [Record<SemanticDOM, CSSProperties>](#semantic-dom) | - | 5.4.0 |
| target | Same as target attribute of a, works when href is specified | string | - |  |
| type | Set button type | `primary` \| `dashed` \| `link` \| `text` \| `default` | `default` |  |
| onClick | Set the handler to handle `click` event | (event: React.MouseEvent<HTMLElement, MouseEvent>) => void | - |  |

It accepts all props which native buttons support.

## Semantic DOM

<code src="./demo/_semantic.tsx" simplify="true"></code>

## Design Token

<ComponentTokenTable component="Button"></ComponentTokenTable>

## FAQ

### How to close the click wave effect?

If you don't need this feature, you can set `disabled` of `wave` in [ConfigProvider](/components/config-provider#api) as `true`.

```jsx
<ConfigProvider wave={{ disabled: true }}>
  <Button>click</Button>
</ConfigProvider>
```

<style>
.site-button-ghost-wrapper {
  padding: 16px;
  background: rgb(190, 200, 200);
}
</style>
