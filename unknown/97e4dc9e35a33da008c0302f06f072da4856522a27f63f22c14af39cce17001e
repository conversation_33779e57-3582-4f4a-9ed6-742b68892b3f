{"printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "jsxBracketSameLine": true, "arrowParens": "always", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": false, "endOfLine": "auto", "embeddedLanguageFormatting": "auto", "plugins": ["prettier-plugin-css-order"], "cssDeclarationSorterOrder": "concentric-css"}