import CalendarLocale from 'rc-picker/lib/locale/sr_RS';

import TimePickerLocale from '../../time-picker/locale/sr_RS';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Izaberi datum',
    yearPlaceholder: 'Izaberi godinu',
    quarterPlaceholder: 'Izaberi tromeseč<PERSON>',
    monthPlaceholder: 'Izaberi mesec',
    weekPlaceholder: 'Iza<PERSON>i sedmicu',
    rangePlaceholder: ['<PERSON><PERSON> početka', 'Datum završetka'],
    rangeYearPlaceholder: ['<PERSON><PERSON> po<PERSON>', '<PERSON><PERSON> završet<PERSON>'],
    rangeMonthPlaceholder: ['Mesec početka', 'Mesec završetka'],
    rangeWeekPlaceholder: ['Sedmica početka', 'Sedmica završetka'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
