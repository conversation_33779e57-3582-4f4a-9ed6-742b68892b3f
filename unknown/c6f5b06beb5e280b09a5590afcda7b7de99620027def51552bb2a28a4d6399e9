import React, { useMemo, useState } from 'react';
import { useDndContext } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS, isKeyboardEvent } from '@dnd-kit/utilities';

import { Input } from '@/components';
import { Position } from './interface';
import { ElementContainer, ElementContent } from './style';

const ElementOverly = (props: any) => {
  const { element = {}, activeId, rows } = props;
  const { activatorEvent, over } = useDndContext();
  // 找到元素所属的行
  const sourceRow = rows.find((row: any) => row.elements.some((el: any) => el.id === element.id));
  const isKeyboardSorting = isKeyboardEvent(activatorEvent);
  const activeIndex = sourceRow.elements.map((el: any) => el.id).indexOf(activeId);
  const overIndex = over?.id ? sourceRow.elements.map((el: any) => el.id).indexOf(over?.id) : -1;

  return (
    <DraggableElement
      element={element}
      isOverly
      rows={rows}
      insertPosition={
        isKeyboardSorting && overIndex !== activeIndex
          ? overIndex > activeIndex
            ? Position.After
            : Position.Before
          : undefined
      }
    />
  );
};

const DraggableElement = (props: any) => {
  const { element = {}, isOverly = false, insertPosition, activeIndex, rows } = props;
  const { id, type, content } = element;
  const [isHovering, setIsHovering] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentContent, setCurrentContent] = useState(content);

  const index = useMemo(() => {
    return rows.flatMap((row: any) => row.elements).findIndex((el: any) => el.id === id);
  }, [rows, id]);

  const { isDragging, isSorting, over, setNodeRef, attributes, listeners, transform, transition } = useSortable({
    id,
    data: {
      index,
      ...element,
    },
    transition: {
      duration: 500,
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });

  const position = isOverly
    ? insertPosition
    : over?.id === id
      ? index > activeIndex
        ? Position.After
        : Position.Before
      : undefined;

  const styles = {
    transform: isSorting ? undefined : CSS.Translate.toString(transform),
    transition,
  };

  const renderContent = () => {
    switch (type) {
      case 'Text':
        return (
          <ElementContent
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            $isHover={isHovering}
            {...listeners}
          >
            {isEditing ? (
              <Input defaultValue={currentContent} onChange={(e) => setCurrentContent(e.target.value)} />
            ) : (
              <div>{currentContent}</div>
            )}
          </ElementContent>
        );
      case 'Button':
        return (
          <ElementContent
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            $isHover={isHovering}
            {...listeners}
          >
            <div>{content}</div>
          </ElementContent>
        );
      default:
        return (
          <ElementContent
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            $isHover={isHovering}
            {...listeners}
          >
            {isEditing ? (
              <Input defaultValue={currentContent} onChange={(e) => setCurrentContent(e.target.value)} />
            ) : (
              <div>{currentContent}</div>
            )}
          </ElementContent>
        );
    }
  };

  return (
    <ElementContainer
      ref={setNodeRef}
      style={styles}
      {...attributes}
      $isDragging={isDragging}
      $isClone={isOverly}
      $insertPosition={position}
      onClick={() => {
        setIsEditing(true);
      }}
    >
      {renderContent()}
    </ElementContainer>
  );
};

export { ElementOverly };

export default DraggableElement;
