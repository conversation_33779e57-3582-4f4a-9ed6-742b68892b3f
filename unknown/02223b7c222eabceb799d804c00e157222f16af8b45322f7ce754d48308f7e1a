## 组件定义

Progress 的本质是了解任务的进度

<code src="./design/behavior-pattern.tsx" inline></code>

## 基础使用

<code src="./design/demo/progress" description="以线形展示总进度和已完成进度，是最基础的使用方式">了解任务进度</code>

<code src="./design/demo/status" description="通过已完成进度的颜色，来了解当前任务的状态">了解任务状态</code>

## 交互变体

<code src="./design/demo/info" description="通过文字和图标，查看进度相关描述">查看进度相关描述</code>

## 样式变体

<code src="./design/demo/circle" description="以环形展示进度，多用于需要强调百分比的场景，如 Dashboard">环形进度条</code>

<code src="./design/demo/content" description="适用于内容级场景的微型进度条，常与文本搭配使用">内容级进度条</code>
