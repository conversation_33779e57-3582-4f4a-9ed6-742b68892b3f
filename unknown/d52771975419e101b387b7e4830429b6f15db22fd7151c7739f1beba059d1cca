import CalendarLocale from 'rc-picker/lib/locale/hr_HR';

import TimePickerLocale from '../../time-picker/locale/hr_HR';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Odaberite datum',
    yearPlaceholder: 'Odaberite godinu',
    quarterPlaceholder: 'Odaberite četvrtinu',
    monthPlaceholder: 'Odaberite mjesec',
    weekPlaceholder: 'Odaberite tjedan',
    rangePlaceholder: ['Po<PERSON><PERSON>ni datum', '<PERSON>avršni datum'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON>na godina', '<PERSON><PERSON><PERSON><PERSON><PERSON> godina'],
    rangeMonthPlaceholder: ['Početni mjesec', '<PERSON>av<PERSON>š<PERSON> mjesec'],
    rangeWeekPlaceholder: ['Početni tjedan', '<PERSON>avršni tjedan'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
