import CalendarLocale from 'rc-picker/lib/locale/fr_BE';

import TimePickerLocale from '../../time-picker/locale/fr_BE';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Sélectionner une date',
    yearPlaceholder: 'Sélectionner une année',
    quarterPlaceholder: 'Sélectionner un trimestre',
    monthPlaceholder: 'Sélectionner un mois',
    weekPlaceholder: 'Sélectionner une semaine',
    rangePlaceholder: ['Date de début', 'Date de fin'],
    rangeYearPlaceholder: ['<PERSON><PERSON> de début', '<PERSON><PERSON> de fin'],
    rangeMonthPlaceholder: ['<PERSON>is de début', '<PERSON>is de fin'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON> de début', '<PERSON><PERSON><PERSON> de fin'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/issues/424

export default locale;
