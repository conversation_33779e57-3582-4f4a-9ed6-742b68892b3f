import CalendarLocale from 'rc-picker/lib/locale/tk_TK';

import TimePickerLocale from '../../time-picker/locale/tk_TK';
import type { PickerLocale } from '../generatePicker';

const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON>t saýla<PERSON>',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> wagty', '<PERSON><PERSON>r<PERSON><PERSON> wagty'],
    yearPlaceholder: 'Ýyl saýlaň',
    quarterPlaceholder: 'Çärýek saýlaň',
    monthPlaceholder: 'Aý saýlaň',
    weekPlaceholder: '<PERSON><PERSON><PERSON> saýlaň',
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly', '<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly'],
    rangeQuarterPlaceholder: ['Başlanýan çärýegi', '<PERSON><PERSON><PERSON><PERSON><PERSON> çärýegi'],
    rangeMonthPlaceholder: ['<PERSON>şlanýan aýy', '<PERSON><PERSON><PERSON>ý<PERSON> aýy'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> hepdesi', '<PERSON><PERSON><PERSON><PERSON><PERSON> hepdesi'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

export default locale;
