import CalendarLocale from 'rc-picker/lib/locale/uz_UZ';

import TimePickerLocale from '../../time-picker/locale/uz_UZ';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Sanani tanlang',
    yearPlaceholder: 'Yilni tanlang',
    quarterPlaceholder: '<PERSON>rak<PERSON> tanlang',
    monthPlaceholder: '<PERSON>yni tanlang',
    weekPlaceholder: 'Haftani tanlang',
    rangePlaceholder: ['Boshlanish sanasi', 'Tugallanish sanasi'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> yili', '<PERSON><PERSON>anish yili'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> oyi', '<PERSON><PERSON><PERSON><PERSON> oyi'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> haftasi', 'Tugallanish haftasi'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
