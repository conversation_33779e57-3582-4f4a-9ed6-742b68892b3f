import CalendarLocale from 'rc-picker/lib/locale/ga_IE';

import TimePickerLocale from '../../time-picker/locale/ga_IE';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Roghnaigh dáta',
    yearPlaceholder: 'Roghnaigh bliain',
    quarterPlaceholder: 'Roghnaigh ráithe',
    monthPlaceholder: 'Roghnaigh mí',
    weekPlaceholder: 'Roghnaigh seachtain',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> tosaigh', '<PERSON><PERSON><PERSON> deiridh'],
    rangeYearPlaceholder: ['Tús na bliana', '<PERSON><PERSON><PERSON><PERSON> na bliana'],
    rangeMonthPlaceholder: ['Tosaigh mhí', 'Deireadh mhí'],
    rangeWeekPlaceholder: ['Tosaigh an tseachtain', 'Deire<PERSON>h na seachtaine'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
