import CalendarLocale from 'rc-picker/lib/locale/lt_LT';

import TimePickerLocale from '../../time-picker/locale/lt_LT';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Pasirinkite datą',
    yearPlaceholder: 'Pasirinkite metus',
    quarterPlaceholder: 'Pasirinkite ketvirtį',
    monthPlaceholder: 'Pasirinkite mėnesį',
    weekPlaceholder: 'Pasirinkite savaitę',
    rangePlaceholder: ['Pradžios data', 'Pabaigos data'],
    rangeYearPlaceholder: ['<PERSON>radž<PERSON> metai', 'Pabaigos metai'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> ketvirtis', 'Pabaigo<PERSON> ketvirtis'],
    rangeMonthPlaceholder: ['Pradž<PERSON> mėnesis', 'Pabaigo<PERSON> mėnesis'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> savaitė', 'Pabaigo<PERSON> savaitė'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
