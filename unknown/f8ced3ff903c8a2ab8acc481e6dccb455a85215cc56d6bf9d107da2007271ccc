import styled from 'styled-components';

const Highlight = styled.div`
  pointer-events: none;
  position: fixed;
  border: 2px solid #20e0d6;
  background: rgba(111, 221, 219, 0.75);
`;

const FloatTips = styled.div`
  pointer-events: none;
  padding: 0 24px;
  position: fixed;
  inset: auto 0 32px;
  margin-left: auto;
  margin-right: auto;
  width: 400px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #424b5e;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.25) 0 0 12px;
  opacity: 0.95;
  color: rgba(255, 255, 255, 0.7);
`;

const Span = styled.span``;

const Key = styled.span`
  font-weight: bold;
  color: #406cf6;
`;

export { Highlight, FloatTips, Span, Key };
