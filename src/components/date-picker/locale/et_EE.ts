import CalendarLocale from 'rc-picker/lib/locale/et_EE';

import TimePickerLocale from '../../time-picker/locale/et_EE';
import type { PickerLocale } from '../generatePicker';

// 统一合并为完整的 Locale
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON> kuup<PERSON>',
    rangePlaceholder: ['<PERSON><PERSON> kuupäev', '<PERSON><PERSON><PERSON> kuup<PERSON>ev'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
