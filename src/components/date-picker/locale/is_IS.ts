import CalendarLocale from 'rc-picker/lib/locale/is_IS';

import TimePickerLocale from '../../time-picker/locale/is_IS';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON><PERSON> dag',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Lokadagur'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
