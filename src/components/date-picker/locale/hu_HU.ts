import CalendarLocale from 'rc-picker/lib/locale/hu_HU';

import TimePickerLocale from '../../time-picker/locale/hu_HU';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON><PERSON><PERSON> dátumot',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> dátum', '<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>tum<PERSON>'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
