import CalendarLocale from 'rc-picker/lib/locale/vi_VN';

import TimePickerLocale from '../../time-picker/locale/vi_VN';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: 'Chọ<PERSON> thời điểm',
    yearPlaceholder: 'Chọ<PERSON> năm',
    quarterPlaceholder: 'Chọn quý',
    monthPlaceholder: '<PERSON>ọ<PERSON> tháng',
    weekPlaceholder: 'Chọn tuần',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeQuarterPlaceholder: ['<PERSON>u<PERSON> bắt đầu', '<PERSON>u<PERSON> kết thúc'],
    rangeMonthPlaceholder: ['<PERSON>h<PERSON><PERSON> bắt đầu', '<PERSON>h<PERSON><PERSON> kết thúc'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', 'Tuần kết thúc'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
