import CalendarLocale from 'rc-picker/lib/locale/kmr_IQ';

import TimePickerLocale from '../../time-picker/locale/kmr_IQ';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON><PERSON> hil<PERSON>',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> destpêkê', '<PERSON><PERSON><PERSON><PERSON> dawîn'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;
