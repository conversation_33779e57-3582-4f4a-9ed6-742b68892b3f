import CalendarLocale from 'rc-picker/lib/locale/id_ID';

import TimePickerLocale from '../../time-picker/locale/id_ID';
import type { PickerLocale } from '../generatePicker';

// Merge into a locale object
const locale: PickerLocale = {
  lang: {
    placeholder: '<PERSON><PERSON><PERSON> tanggal',
    yearPlaceholder: '<PERSON><PERSON>h tahun',
    quarterPlaceholder: '<PERSON><PERSON><PERSON> kuartal',
    monthPlaceholder: '<PERSON><PERSON><PERSON> bulan',
    weekPlaceholder: '<PERSON><PERSON>h minggu',
    rangePlaceholder: ['<PERSON>gal awal', 'Tanggal akhir'],
    rangeYearPlaceholder: ['<PERSON>hun awal', '<PERSON>hun akhir'],
    rangeQuarterPlaceholder: ['<PERSON>artal awal', 'Kuartal akhir'],
    rangeMonthPlaceholder: ['<PERSON>ulan awal', '<PERSON>ula<PERSON> akhir'],
    rangeWeekPlaceholder: ['<PERSON>gu awal', '<PERSON>gu akhir'],
    ...CalendarLocale,
  },
  timePickerLocale: {
    ...TimePickerLocale,
  },
};

// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

export default locale;
