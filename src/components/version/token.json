{"Upload": {"global": ["fontSizeHeading3", "fontHeight", "lineWidth", "controlHeightLG", "colorTextDisabled", "colorText", "fontSize", "lineHeight", "fontFamily", "colorFillAlter", "colorBorder", "borderRadiusLG", "motionDurationSlow", "padding", "lineWidthFocus", "colorPrimaryBorder", "colorPrimaryHover", "margin", "colorPrimary", "marginXXS", "colorTextHeading", "fontSizeLG", "colorTextDescription", "paddingXS", "lineType", "paddingSM", "fontSizeHeading2", "colorError", "colorErrorBg", "colorTextLightSolid", "marginXS", "colorBgMask", "marginXL", "fontHeightSM", "controlItemBgHover", "motionEaseInOutCirc", "motionDurationMid", "motionEaseInOut"], "component": {"actionsColor": "rgba(0, 0, 0, 0.45)"}}, "Typography": {"global": ["colorText", "lineHeight", "colorTextDescription", "colorSuccess", "colorWarning", "colorError", "colorErrorActive", "colorErrorHover", "colorTextDisabled", "fontSizeHeading1", "lineHeightHeading1", "colorTextHeading", "fontWeightStrong", "fontSizeHeading2", "lineHeightHeading2", "fontSizeHeading3", "lineHeightHeading3", "fontSizeHeading4", "lineHeightHeading4", "fontSizeHeading5", "lineHeightHeading5", "fontFamilyCode", "colorLink", "motionDurationSlow", "colorLinkHover", "colorLinkActive", "linkDecoration", "linkHoverDecoration", "marginXXS", "paddingSM", "marginXS", "fontSize"], "component": {"titleMarginTop": "1.2em", "titleMarginBottom": "0.5em"}}, "TreeSelect": {"global": ["colorBgElevated", "paddingXS", "colorText", "fontSize", "lineHeight", "fontFamily", "borderRadius", "motionDurationSlow", "lineWidthFocus", "colorPrimaryBorder", "colorPrimary", "colorTextDisabled", "controlItemBgHover", "colorBgTextHover", "colorBorder", "marginXXS", "motionDurationMid", "lineWidthBold", "controlInteractiveSize", "marginXS", "borderRadiusSM", "colorBgContainer", "lineWidth", "lineType", "colorWhite", "motionDurationFast", "motionEaseInBack", "colorPrimaryHover", "motionEaseOutBack", "fontSizeLG", "colorBgContainerDisabled"], "component": {"titleHeight": 24, "nodeHoverBg": "rgba(0, 0, 0, 0.04)", "nodeSelectedBg": "#e6f4ff"}}, "Transfer": {"global": ["marginXS", "marginXXS", "fontSizeIcon", "colorBgContainerDisabled", "colorText", "fontSize", "lineHeight", "fontFamily", "colorBorder", "colorSplit", "lineWidth", "controlItemBgActive", "colorTextDisabled", "paddingSM", "lineType", "motionDurationSlow", "controlItemBgHover", "borderRadiusLG", "colorBgContainer", "controlItemBgActiveHover", "colorLinkHover", "paddingXS", "controlHeightLG", "colorError", "colorWarning"], "component": {"listWidth": 180, "listHeight": 200, "listWidthLG": 250, "headerHeight": 40, "itemHeight": 32, "itemPaddingBlock": 5, "transferHeaderVerticalPadding": 9}}, "Tree": {"global": ["controlInteractiveSize", "colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "borderRadiusSM", "lineWidthFocus", "colorPrimaryBorder", "colorBgContainer", "lineWidth", "lineType", "colorBorder", "motionDurationSlow", "lineWidthBold", "colorWhite", "motionDurationFast", "motionEaseInBack", "paddingXS", "colorPrimary", "colorPrimaryHover", "motionDurationMid", "motionEaseOutBack", "fontSizeLG", "colorBgContainerDisabled", "colorTextDisabled", "borderRadius", "controlItemBgHover", "colorBgTextHover", "marginXXS", "motionEaseInOut"], "component": {"titleHeight": 24, "nodeHoverBg": "rgba(0, 0, 0, 0.04)", "nodeSelectedBg": "#e6f4ff", "directoryNodeSelectedColor": "#fff", "directoryNodeSelectedBg": "#1677ff"}}, "Tour": {"global": ["borderRadiusLG", "lineHeight", "padding", "paddingXS", "borderRadius", "borderRadiusXS", "colorPrimary", "colorText", "colorFill", "boxShadowTertiary", "fontSize", "colorBgElevated", "fontWeightStrong", "marginXS", "colorTextLightSolid", "colorWhite", "motionDurationSlow", "fontFamily", "colorIcon", "borderRadiusSM", "motionDurationMid", "colorIconHover", "colorBgTextHover", "colorBgTextActive", "lineWidthFocus", "colorPrimaryBorder", "boxShadowPopoverArrow", "sizePopupArrow"], "component": {"zIndexPopup": 1070, "closeBtnSize": 22, "primaryPrevBtnBg": "rgba(255, 255, 255, 0.15)", "primaryNextBtnHoverBg": "rgb(240, 240, 240)", "arrowOffsetHorizontal": 12, "arrowOffsetVertical": 8, "arrowShadowWidth": 8.970562748477143, "arrowPath": "path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z')", "arrowPolygon": "polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%)"}}, "Timeline": {"global": ["paddingXXS", "colorText", "fontSize", "lineHeight", "fontFamily", "lineType", "fontSizeSM", "colorPrimary", "colorError", "colorSuccess", "colorTextDisabled", "lineWidth", "margin", "controlHeightLG", "marginXXS", "marginSM", "marginXS"], "component": {"tailColor": "rgba(5, 5, 5, 0.06)", "tailWidth": 2, "dotBorderWidth": 2, "dotBg": "#ffffff", "itemPaddingBottom": 20}}, "Tooltip": {"global": ["borderRadius", "colorTextLightSolid", "colorBgSpotlight", "controlHeight", "boxShadowSecondary", "paddingSM", "paddingXS", "colorText", "fontSize", "lineHeight", "fontFamily", "blue1", "blue3", "blue6", "blue7", "purple1", "purple3", "purple6", "purple7", "cyan1", "cyan3", "cyan6", "cyan7", "green1", "green3", "green6", "green7", "magenta1", "magenta3", "magenta6", "magenta7", "pink1", "pink3", "pink6", "pink7", "red1", "red3", "red6", "red7", "orange1", "orange3", "orange6", "orange7", "yellow1", "yellow3", "yellow6", "yellow7", "volcano1", "volcano3", "volcano6", "volcano7", "geekblue1", "geekblue3", "geekblue6", "geekblue7", "lime1", "lime3", "lime6", "lime7", "gold1", "gold3", "gold6", "gold7", "boxShadowPopoverArrow", "sizePopupArrow", "borderRadiusXS", "motionDurationFast", "motionEaseOutCirc", "motionEaseInOutCirc"], "component": {"zIndexPopup": 1070, "arrowOffsetHorizontal": 12, "arrowOffsetVertical": 8, "arrowShadowWidth": 8.970562748477143, "arrowPath": "path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z')", "arrowPolygon": "polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%)"}}, "Tag": {"global": ["lineWidth", "fontSizeIcon", "fontSizeSM", "lineHeightSM", "paddingXXS", "colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "lineType", "colorBorder", "borderRadiusSM", "motionDurationMid", "colorTextDescription", "colorTextHeading", "colorTextLightSolid", "colorPrimary", "colorFillSecondary", "colorPrimaryHover", "colorPrimaryActive"], "component": {"defaultBg": "#fafafa", "defaultColor": "rgba(0, 0, 0, 0.88)"}}, "Tabs": {"global": ["paddingXXS", "borderRadius", "marginSM", "marginXS", "marginXXS", "margin", "colorBorderSecondary", "lineWidth", "lineType", "lineWidthBold", "motionDurationSlow", "controlHeight", "boxShadowTabsOverflowLeft", "boxShadowTabsOverflowRight", "boxShadowTabsOverflowTop", "boxShadowTabsOverflowBottom", "colorBorder", "paddingLG", "colorText", "fontSize", "lineHeight", "fontFamily", "colorBgContainer", "borderRadiusLG", "boxShadowSecondary", "paddingSM", "colorTextDescription", "fontSizeSM", "controlItemBgHover", "colorTextDisabled", "motionEaseInOut", "controlHeightLG", "paddingXS", "lineWidthFocus", "colorPrimaryBorder", "colorTextHeading", "motionDurationMid", "motionEaseOutQuint", "motionEaseInQuint"], "component": {"zIndexPopup": 1050, "cardBg": "rgba(0, 0, 0, 0.02)", "cardHeight": 40, "cardPadding": "8px 16px", "cardPaddingSM": "6px 16px", "cardPaddingLG": "8px 16px 6px", "titleFontSize": 14, "titleFontSizeLG": 16, "titleFontSizeSM": 14, "inkBarColor": "#1677ff", "horizontalMargin": "0 0 16px 0", "horizontalItemGutter": 32, "horizontalItemMargin": "", "horizontalItemMarginRTL": "", "horizontalItemPadding": "12px 0", "horizontalItemPaddingSM": "8px 0", "horizontalItemPaddingLG": "16px 0", "verticalItemPadding": "8px 24px", "verticalItemMargin": "16px 0 0 0", "itemColor": "rgba(0, 0, 0, 0.88)", "itemSelectedColor": "#1677ff", "itemHoverColor": "#4096ff", "itemActiveColor": "#0958d9", "cardGutter": 2}}, "Steps": {"global": ["colorTextDisabled", "controlHeightLG", "colorTextLightSolid", "colorText", "colorPrimary", "colorTextDescription", "colorTextQuaternary", "colorError", "colorBorderSecondary", "colorSplit", "fontSize", "lineHeight", "fontFamily", "motionDurationSlow", "lineWidthFocus", "colorPrimaryBorder", "marginXS", "lineWidth", "lineType", "padding", "fontSizeLG", "fontWeightStrong", "fontSizeSM", "paddingSM", "paddingXXS", "margin", "controlHeight", "marginXXS", "paddingLG", "marginSM", "paddingXS", "controlHeightSM", "fontSizeIcon", "lineWidthBold", "marginLG", "borderRadiusSM", "motionDurationMid", "controlItemBgHover", "lineHeightSM", "colorBorderBg"], "component": {"titleLineHeight": 32, "customIconSize": 32, "customIconTop": 0, "customIconFontSize": 24, "iconSize": 32, "iconTop": -0.5, "iconFontSize": 14, "iconSizeSM": 24, "dotSize": 8, "dotCurrentSize": 10, "navArrowColor": "rgba(0, 0, 0, 0.25)", "navContentMaxWidth": "auto", "descriptionMaxWidth": 140, "waitIconColor": "rgba(0, 0, 0, 0.25)", "waitIconBgColor": "#ffffff", "waitIconBorderColor": "rgba(0, 0, 0, 0.25)", "finishIconBgColor": "#ffffff", "finishIconBorderColor": "#1677ff"}}, "Switch": {"global": ["motionDurationMid", "colorPrimary", "opacityLoading", "fontSizeIcon", "colorText", "fontSize", "lineHeight", "fontFamily", "colorTextQuaternary", "colorTextTertiary", "lineWidthFocus", "colorPrimaryBorder", "colorPrimaryHover", "colorTextLightSolid", "fontSizeSM", "marginXXS"], "component": {"trackHeight": 22, "trackHeightSM": 16, "trackMinWidth": 44, "trackMinWidthSM": 28, "trackPadding": 2, "handleBg": "#fff", "handleSize": 18, "handleSizeSM": 12, "handleShadow": "0 2px 4px 0 rgba(0, 35, 11, 0.2)", "innerMinMargin": 9, "innerMaxMargin": 24, "innerMinMarginSM": 6, "innerMaxMarginSM": 18}}, "Statistic": {"global": ["marginXXS", "padding", "colorTextDescription", "colorTextHeading", "fontFamily", "colorText", "fontSize", "lineHeight"], "component": {"titleFontSize": 14, "contentFontSize": 24}}, "Table": {"global": ["colorTextHeading", "colorSplit", "colorBgContainer", "controlInteractiveSize", "padding", "fontWeightStrong", "lineWidth", "lineType", "motionDurationMid", "colorText", "fontSize", "lineHeight", "fontFamily", "margin", "paddingXS", "marginXXS", "fontSizeIcon", "motionDurationSlow", "colorPrimary", "paddingXXS", "fontSizeSM", "borderRadius", "colorTextDescription", "colorTextDisabled", "controlItemBgHover", "controlItemBgActive", "boxShadowSecondary", "colorLink", "colorLinkHover", "colorLinkActive", "opacityLoading"], "component": {"headerBg": "#fafafa", "headerColor": "rgba(0, 0, 0, 0.88)", "headerSortActiveBg": "#f0f0f0", "headerSortHoverBg": "#f0f0f0", "bodySortBg": "#fafafa", "rowHoverBg": "#fafafa", "rowSelectedBg": "#e6f4ff", "rowSelectedHoverBg": "#bae0ff", "rowExpandedBg": "rgba(0, 0, 0, 0.02)", "cellPaddingBlock": 16, "cellPaddingInline": 16, "cellPaddingBlockMD": 12, "cellPaddingInlineMD": 8, "cellPaddingBlockSM": 8, "cellPaddingInlineSM": 8, "borderColor": "#f0f0f0", "headerBorderRadius": 8, "footerBg": "#fafafa", "footerColor": "rgba(0, 0, 0, 0.88)", "cellFontSize": 14, "cellFontSizeMD": 14, "cellFontSizeSM": 14, "headerSplitColor": "#f0f0f0", "fixedHeaderSortActiveBg": "#f0f0f0", "headerFilterHoverBg": "rgba(0, 0, 0, 0.06)", "filterDropdownMenuBg": "#ffffff", "filterDropdownBg": "#ffffff", "expandIconBg": "#ffffff", "selectionColumnWidth": 32, "stickyScrollBarBg": "rgba(0, 0, 0, 0.25)", "stickyScrollBarBorderRadius": 100, "expandIconMarginTop": 2.5, "headerIconColor": "rgba(0, 0, 0, 0.29)", "headerIconHoverColor": "rgba(0, 0, 0, 0.57)", "expandIconHalfInner": 7, "expandIconSize": 17, "expandIconScale": 0.9411764705882353}}, "Spin": {"global": ["colorTextDescription", "colorText", "fontSize", "lineHeight", "fontFamily", "colorPrimary", "motionDurationSlow", "motionEaseInOutCirc", "colorBgMask", "zIndexPopupBase", "motionDurationMid", "colorWhite", "colorTextLightSolid", "colorBgContainer", "marginXXS", "colorFillSecondary"], "component": {"contentHeight": 400, "dotSize": 20, "dotSizeSM": 14, "dotSizeLG": 32}}, "Slider": {"global": ["controlHeight", "controlHeightLG", "colorFillContentHover", "colorText", "fontSize", "lineHeight", "fontFamily", "borderRadiusXS", "motionDurationMid", "colorPrimaryBorderHover", "colorBgElevated", "colorTextDescription", "motionDurationSlow"], "component": {"controlSize": 10, "railSize": 4, "handleSize": 10, "handleSizeHover": 12, "dotSize": 8, "handleLineWidth": 2, "handleLineWidthHover": 4, "railBg": "rgba(0, 0, 0, 0.04)", "railHoverBg": "rgba(0, 0, 0, 0.06)", "trackBg": "#91caff", "trackHoverBg": "#69b1ff", "handleColor": "#91caff", "handleActiveColor": "#1677ff", "handleColorDisabled": "#bfbfbf", "dotBorderColor": "#f0f0f0", "dotActiveBorderColor": "#91caff", "trackBgDisabled": "rgba(0, 0, 0, 0.04)"}}, "Skeleton": {"global": ["controlHeight", "controlHeightLG", "controlHeightSM", "padding", "marginSM", "controlHeightXS", "borderRadiusSM"], "component": {"color": "rgba(0, 0, 0, 0.06)", "colorGradientEnd": "rgba(0, 0, 0, 0.15)", "gradientFromColor": "rgba(0, 0, 0, 0.06)", "gradientToColor": "rgba(0, 0, 0, 0.15)", "titleHeight": 16, "blockRadius": 4, "paragraphMarginTop": 28, "paragraphLiHeight": 16}}, "Select": {"global": ["paddingSM", "controlHeight", "colorText", "fontSize", "lineHeight", "fontFamily", "motionDurationMid", "motionEaseInOut", "colorTextPlaceholder", "fontSizeIcon", "colorTextQuaternary", "motionDurationSlow", "colorTextTertiary", "paddingXS", "controlPaddingHorizontalSM", "lineWidth", "borderRadius", "controlHeightSM", "borderRadiusSM", "fontSizeLG", "borderRadiusLG", "borderRadiusXS", "controlHeightLG", "controlPaddingHorizontal", "paddingXXS", "colorIcon", "colorIconHover", "colorBgElevated", "boxShadowSecondary", "colorTextDescription", "fontSizeSM", "colorPrimary", "colorBgContainerDisabled", "colorTextDisabled", "motionEaseOutQuint", "motionEaseInQuint", "motionEaseOutCirc", "motionEaseInOutCirc", "colorBorder", "colorPrimaryHover", "controlOutline", "controlOutlineWidth", "lineType", "colorError", "colorErrorHover", "colorErrorOutline", "colorWarning", "colorWarningHover", "colorWarningOutline", "colorFillTertiary", "colorFillSecondary", "colorErrorBg", "colorErrorBgHover", "colorWarningBg", "colorWarningBgHover", "colorBgContainer", "colorSplit"], "component": {"INTERNAL_FIXED_ITEM_MARGIN": 2, "zIndexPopup": 1050, "optionSelectedColor": "rgba(0, 0, 0, 0.88)", "optionSelectedFontWeight": 600, "optionSelectedBg": "#e6f4ff", "optionActiveBg": "rgba(0, 0, 0, 0.04)", "optionPadding": "5px 12px", "optionFontSize": 14, "optionLineHeight": 1.5714285714285714, "optionHeight": 32, "selectorBg": "#ffffff", "clearBg": "#ffffff", "singleItemHeightLG": 40, "multipleItemBg": "rgba(0, 0, 0, 0.06)", "multipleItemBorderColor": "transparent", "multipleItemHeight": 24, "multipleItemHeightSM": 16, "multipleItemHeightLG": 32, "multipleSelectorBgDisabled": "rgba(0, 0, 0, 0.04)", "multipleItemColorDisabled": "rgba(0, 0, 0, 0.25)", "multipleItemBorderColorDisabled": "transparent", "showArrowPaddingInlineEnd": 18}}, "Space": {"global": ["paddingXS", "padding", "paddingLG"], "component": {}}, "Segmented": {"global": ["lineWidth", "controlPaddingHorizontal", "controlPaddingHorizontalSM", "controlHeight", "controlHeightLG", "controlHeightSM", "colorText", "fontSize", "lineHeight", "fontFamily", "borderRadius", "motionDurationMid", "motionEaseInOut", "borderRadiusSM", "boxShadowTertiary", "marginSM", "paddingXXS", "borderRadiusLG", "fontSizeLG", "borderRadiusXS", "colorTextDisabled", "motionDurationSlow"], "component": {"trackPadding": 2, "trackBg": "#f5f5f5", "itemColor": "rgba(0, 0, 0, 0.65)", "itemHoverColor": "rgba(0, 0, 0, 0.88)", "itemHoverBg": "rgba(0, 0, 0, 0.06)", "itemSelectedBg": "#ffffff", "itemActiveBg": "rgba(0, 0, 0, 0.15)", "itemSelectedColor": "rgba(0, 0, 0, 0.88)"}}, "Result": {"global": ["colorInfo", "colorError", "colorSuccess", "colorWarning", "lineHeightHeading3", "padding", "paddingXL", "paddingXS", "paddingLG", "marginXS", "lineHeight", "colorTextHeading", "colorTextDescription", "colorFillAlter"], "component": {"titleFontSize": 24, "subtitleFontSize": 14, "iconFontSize": 72, "extraMargin": "24px 0 0 0"}}, "Rate": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "motionDurationMid", "lineWidth"], "component": {"starColor": "#fadb14", "starSize": 20, "starHoverScale": "scale(1.1)", "starBg": "rgba(0, 0, 0, 0.06)"}}, "Radio": {"global": ["controlOutline", "controlOutlineWidth", "colorText", "fontSize", "lineHeight", "fontFamily", "colorPrimary", "motionDurationSlow", "motionDurationMid", "motionEaseInOutCirc", "colorBgContainer", "colorBorder", "lineWidth", "colorBgContainerDisabled", "colorTextDisabled", "paddingXS", "lineType", "lineWidthFocus", "colorPrimaryBorder", "controlHeight", "fontSizeLG", "controlHeightLG", "controlHeightSM", "borderRadius", "borderRadiusSM", "borderRadiusLG", "colorPrimaryHover", "colorPrimaryActive"], "component": {"radioSize": 16, "dotSize": 8, "dotColorDisabled": "rgba(0, 0, 0, 0.25)", "buttonSolidCheckedColor": "#fff", "buttonSolidCheckedBg": "#1677ff", "buttonSolidCheckedHoverBg": "#4096ff", "buttonSolidCheckedActiveBg": "#0958d9", "buttonBg": "#ffffff", "buttonCheckedBg": "#ffffff", "buttonColor": "rgba(0, 0, 0, 0.88)", "buttonCheckedBgDisabled": "rgba(0, 0, 0, 0.15)", "buttonCheckedColorDisabled": "rgba(0, 0, 0, 0.25)", "buttonPaddingInline": 15, "wrapperMarginInlineEnd": 8, "radioColor": "#1677ff", "radioBgColor": "#ffffff"}}, "QRCode": {"global": ["colorText", "lineWidth", "lineType", "colorSplit", "fontSize", "lineHeight", "fontFamily", "paddingSM", "colorWhite", "borderRadiusLG", "marginXS", "controlHeight"], "component": {"QRCodeMaskBackgroundColor": "rgba(255, 255, 255, 0.96)"}}, "Progress": {"global": ["marginXXS", "colorText", "fontSize", "lineHeight", "fontFamily", "motionDurationSlow", "motionEaseInOutCirc", "colorWhite", "colorSuccess", "marginXS", "paddingXXS", "colorBgContainer", "motionEaseOutQuint", "colorError", "fontSizeSM"], "component": {"circleTextColor": "rgba(0, 0, 0, 0.88)", "defaultColor": "#1677ff", "remainingColor": "rgba(0, 0, 0, 0.06)", "lineBorderRadius": 100, "circleTextFontSize": "1em", "circleIconFontSize": "1.1666666666666667em"}}, "Popover": {"global": ["colorBgElevated", "colorText", "fontWeightStrong", "boxShadowSecondary", "colorTextHeading", "borderRadiusLG", "fontSize", "lineHeight", "fontFamily", "boxShadowPopoverArrow", "sizePopupArrow", "borderRadiusXS", "blue6", "purple6", "cyan6", "green6", "magenta6", "pink6", "red6", "orange6", "yellow6", "volcano6", "geekblue6", "lime6", "gold6", "motionDurationMid", "motionEaseOutCirc", "motionEaseInOutCirc"], "component": {"titleMinWidth": 177, "zIndexPopup": 1030, "arrowShadowWidth": 8.970562748477143, "arrowPath": "path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z')", "arrowPolygon": "polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%)", "arrowOffsetHorizontal": 12, "arrowOffsetVertical": 8, "innerPadding": 0, "titleMarginBottom": 0, "titlePadding": "5px 16px 4px", "titleBorderBottom": "1px solid rgba(5, 5, 5, 0.06)", "innerContentPadding": "12px 16px"}}, "Popconfirm": {"global": ["colorText", "colorWarning", "marginXXS", "marginXS", "fontSize", "fontWeightStrong", "colorTextHeading"], "component": {"zIndexPopup": 1060}}, "Pagination": {"global": ["marginXXS", "controlHeightLG", "marginSM", "paddingXXS", "colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "lineWidth", "lineType", "borderRadius", "motionDurationMid", "colorBgTextHover", "colorBgTextActive", "fontWeightStrong", "colorPrimary", "colorPrimaryHover", "fontSizeSM", "colorTextDisabled", "margin", "controlHeight", "colorTextPlaceholder", "motionDurationSlow", "lineHeightLG", "borderRadiusLG", "borderRadiusSM", "colorBorder", "colorBgContainer", "colorBgContainerDisabled", "controlOutlineWidth", "controlOutline", "controlHeightSM", "screenLG", "screenSM", "lineWidthFocus", "colorPrimaryBorder"], "component": {"itemBg": "#ffffff", "itemSize": 32, "itemSizeSM": 24, "itemActiveBg": "#ffffff", "itemLinkBg": "#ffffff", "itemActiveColorDisabled": "rgba(0, 0, 0, 0.25)", "itemActiveBgDisabled": "rgba(0, 0, 0, 0.15)", "itemInputBg": "#ffffff", "miniOptionsSizeChangerTop": 0, "paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7, "paddingInline": 11, "paddingInlineSM": 7, "paddingInlineLG": 11, "addonBg": "rgba(0, 0, 0, 0.02)", "activeBorderColor": "#1677ff", "hoverBorderColor": "#4096ff", "activeShadow": "0 0 0 2px rgba(5, 145, 255, 0.1)", "errorActiveShadow": "0 0 0 2px rgba(255, 38, 5, 0.06)", "warningActiveShadow": "0 0 0 2px rgba(255, 215, 5, 0.1)", "hoverBg": "#ffffff", "activeBg": "#ffffff", "inputFontSize": 14, "inputFontSizeLG": 16, "inputFontSizeSM": 14}}, "Notification": {"global": ["paddingMD", "paddingLG", "colorBgElevated", "fontSizeLG", "lineHeightLG", "controlHeightLG", "margin", "paddingContentHorizontalLG", "marginLG", "colorPrimaryBorderHover", "colorPrimary", "motionDurationMid", "motionEaseInOut", "colorText", "fontSize", "lineHeight", "fontFamily", "boxShadow", "borderRadiusLG", "colorSuccess", "colorInfo", "colorWarning", "colorError", "colorTextHeading", "marginXS", "marginSM", "colorIcon", "borderRadiusSM", "colorIconHover", "colorBgTextHover", "colorBgTextActive", "lineWidthFocus", "colorPrimaryBorder", "motionDurationSlow", "colorBgBlur"], "component": {"zIndexPopup": 2050, "width": 384}}, "Modal": {"global": ["padding", "fontSizeHeading5", "lineHeightHeading5", "colorSplit", "lineType", "lineWidth", "colorIcon", "colorIconHover", "controlHeight", "fontHeight", "screenSMMax", "marginXS", "colorText", "fontSize", "lineHeight", "fontFamily", "margin", "paddingLG", "fontWeightStrong", "borderRadiusLG", "boxShadow", "zIndexPopupBase", "borderRadiusSM", "motionDurationMid", "fontSizeLG", "colorBgTextHover", "colorBgTextActive", "lineWidthFocus", "colorPrimaryBorder", "motionDurationSlow", "colorBgMask", "motionEaseOutCirc", "motionEaseInOutCirc"], "component": {"footerBg": "transparent", "headerBg": "#ffffff", "titleLineHeight": 1.5, "titleFontSize": 16, "contentBg": "#ffffff", "titleColor": "rgba(0, 0, 0, 0.88)", "contentPadding": 0, "headerPadding": "16px 24px", "headerBorderBottom": "1px solid rgba(5, 5, 5, 0.06)", "headerMarginBottom": 0, "bodyPadding": 24, "footerPadding": "8px 16px", "footerBorderTop": "1px solid rgba(5, 5, 5, 0.06)", "footerBorderRadius": "0 0 8px 8px", "footerMarginTop": 0, "confirmBodyPadding": "32px 32px 24px", "confirmIconMarginInlineEnd": 16, "confirmBtnsMarginTop": 24}}, "Message": {"global": ["boxShadow", "colorText", "colorSuccess", "colorError", "colorWarning", "colorInfo", "fontSizeLG", "motionEaseInOutCirc", "motionDurationSlow", "marginXS", "paddingXS", "borderRadiusLG", "fontSize", "lineHeight", "fontFamily"], "component": {"zIndexPopup": 2010, "contentBg": "#ffffff", "contentPadding": "9px 12px"}}, "Menu": {"global": ["colorBgElevated", "controlHeightLG", "fontSize", "motionDurationSlow", "motionDurationMid", "motionEaseInOut", "paddingXS", "padding", "colorSplit", "lineWidth", "borderRadiusLG", "lineType", "colorText", "lineHeight", "fontFamily", "motionEaseOut", "borderRadius", "margin", "colorTextLightSolid", "paddingXL", "fontSizeLG", "boxShadowSecondary", "marginXS", "lineWidthFocus", "colorPrimaryBorder", "motionEaseOutQuint", "motionEaseInQuint", "motionEaseOutCirc", "motionEaseInOutCirc"], "component": {"dropdownWidth": 160, "zIndexPopup": 1050, "radiusItem": 8, "itemBorderRadius": 8, "radiusSubMenuItem": 4, "subMenuItemBorderRadius": 4, "colorItemText": "rgba(0, 0, 0, 0.88)", "itemColor": "rgba(0, 0, 0, 0.88)", "colorItemTextHover": "rgba(0, 0, 0, 0.88)", "itemHoverColor": "rgba(0, 0, 0, 0.88)", "colorItemTextHoverHorizontal": "#1677ff", "horizontalItemHoverColor": "#1677ff", "colorGroupTitle": "rgba(0, 0, 0, 0.45)", "groupTitleColor": "rgba(0, 0, 0, 0.45)", "colorItemTextSelected": "#1677ff", "itemSelectedColor": "#1677ff", "colorItemTextSelectedHorizontal": "#1677ff", "horizontalItemSelectedColor": "#1677ff", "colorItemBg": "#ffffff", "itemBg": "#ffffff", "colorItemBgHover": "rgba(0, 0, 0, 0.06)", "itemHoverBg": "rgba(0, 0, 0, 0.06)", "colorItemBgActive": "rgba(0, 0, 0, 0.06)", "itemActiveBg": "#e6f4ff", "colorSubItemBg": "rgba(0, 0, 0, 0.02)", "subMenuItemBg": "rgba(0, 0, 0, 0.02)", "colorItemBgSelected": "#e6f4ff", "itemSelectedBg": "#e6f4ff", "colorItemBgSelectedHorizontal": "transparent", "horizontalItemSelectedBg": "transparent", "colorActiveBarWidth": 0, "activeBarWidth": 0, "colorActiveBarHeight": 2, "activeBarHeight": 2, "colorActiveBarBorderSize": 1, "activeBarBorderWidth": 1, "colorItemTextDisabled": "rgba(0, 0, 0, 0.25)", "itemDisabledColor": "rgba(0, 0, 0, 0.25)", "colorDangerItemText": "#ff4d4f", "dangerItemColor": "#ff4d4f", "colorDangerItemTextHover": "#ff4d4f", "dangerItemHoverColor": "#ff4d4f", "colorDangerItemTextSelected": "#ff4d4f", "dangerItemSelectedColor": "#ff4d4f", "colorDangerItemBgActive": "#fff2f0", "dangerItemActiveBg": "#fff2f0", "colorDangerItemBgSelected": "#fff2f0", "dangerItemSelectedBg": "#fff2f0", "itemMarginInline": 4, "horizontalItemBorderRadius": 0, "horizontalItemHoverBg": "transparent", "itemHeight": 40, "groupTitleLineHeight": 1.5714285714285714, "collapsedWidth": 80, "popupBg": "#ffffff", "itemMarginBlock": 4, "itemPaddingInline": 16, "horizontalLineHeight": "46px", "iconSize": 14, "iconMarginInlineEnd": 10, "collapsedIconSize": 16, "groupTitleFontSize": 14, "darkItemDisabledColor": "rgba(255, 255, 255, 0.25)", "darkItemColor": "rgba(255, 255, 255, 0.65)", "darkDangerItemColor": "#ff4d4f", "darkItemBg": "#001529", "darkPopupBg": "#001529", "darkSubMenuItemBg": "#000c17", "darkItemSelectedColor": "#fff", "darkItemSelectedBg": "#1677ff", "darkDangerItemSelectedBg": "#ff4d4f", "darkItemHoverBg": "transparent", "darkGroupTitleColor": "rgba(255, 255, 255, 0.65)", "darkItemHoverColor": "#fff", "darkDangerItemHoverColor": "#ff7875", "darkDangerItemSelectedColor": "#fff", "darkDangerItemActiveBg": "#ff4d4f", "itemWidth": "calc(100% - 8px)"}}, "Mentions": {"global": ["paddingXXS", "colorTextDisabled", "controlItemBgHover", "controlPaddingHorizontal", "colorText", "motionDurationSlow", "lineHeight", "controlHeight", "fontSize", "fontSizeIcon", "colorTextTertiary", "colorTextQuaternary", "colorBgElevated", "paddingLG", "borderRadius", "borderRadiusLG", "boxShadowSecondary", "fontFamily", "motionDurationMid", "colorTextPlaceholder", "lineHeightLG", "borderRadiusSM", "colorBorder", "colorBgContainer", "lineWidth", "lineType", "colorBgContainerDisabled", "colorError", "colorErrorBorderHover", "colorWarning", "colorWarningBorderHover", "colorFillTertiary", "colorFillSecondary", "colorPrimary", "colorErrorBg", "colorErrorBgHover", "colorErrorText", "colorWarningBg", "colorWarningBgHover", "colorWarningText", "fontWeightStrong"], "component": {"paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7, "paddingInline": 11, "paddingInlineSM": 7, "paddingInlineLG": 11, "addonBg": "rgba(0, 0, 0, 0.02)", "activeBorderColor": "#1677ff", "hoverBorderColor": "#4096ff", "activeShadow": "0 0 0 2px rgba(5, 145, 255, 0.1)", "errorActiveShadow": "0 0 0 2px rgba(255, 38, 5, 0.06)", "warningActiveShadow": "0 0 0 2px rgba(255, 215, 5, 0.1)", "hoverBg": "#ffffff", "activeBg": "#ffffff", "inputFontSize": 14, "inputFontSizeLG": 16, "inputFontSizeSM": 14, "dropdownHeight": 250, "controlItemWidth": 100, "zIndexPopup": 1050, "itemPaddingVertical": 5}}, "List": {"global": ["controlHeightLG", "controlHeight", "paddingSM", "marginLG", "padding", "colorPrimary", "paddingXS", "margin", "colorText", "colorTextDescription", "motionDurationSlow", "lineWidth", "fontSize", "lineHeight", "fontFamily", "marginXXS", "marginXXL", "fontHeight", "colorSplit", "fontSizeSM", "colorTextDisabled", "fontSizeLG", "lineHeightLG", "lineType", "paddingLG", "borderRadiusLG", "colorBorder", "screenSM", "screenMD", "marginSM"], "component": {"contentWidth": 220, "itemPadding": "12px 0", "itemPaddingSM": "8px 16px", "itemPaddingLG": "16px 24px", "headerBg": "transparent", "footerBg": "transparent", "emptyTextPadding": 16, "metaMarginBottom": 16, "avatarMarginRight": 16, "titleMarginBottom": 12, "descriptionFontSize": 14}}, "Layout": {"global": ["colorText", "motionDurationMid", "motionDurationSlow", "fontSize", "borderRadius", "fontSizeXL"], "component": {"colorBgHeader": "#001529", "colorBgBody": "#f5f5f5", "colorBgTrigger": "#002140", "bodyBg": "#f5f5f5", "headerBg": "#001529", "headerHeight": 64, "headerPadding": "0 50px", "headerColor": "rgba(0, 0, 0, 0.88)", "footerPadding": "24px 50px", "footerBg": "#f5f5f5", "siderBg": "#001529", "triggerHeight": 48, "triggerBg": "#002140", "triggerColor": "#fff", "zeroTriggerWidth": 40, "zeroTriggerHeight": 40, "lightSiderBg": "#ffffff", "lightTriggerBg": "#ffffff", "lightTriggerColor": "rgba(0, 0, 0, 0.88)"}}, "InputNumber": {"global": ["paddingXXS", "lineWidth", "lineType", "borderRadius", "controlHeightLG", "controlHeightSM", "colorError", "colorTextDescription", "motionDurationMid", "colorTextDisabled", "borderRadiusSM", "borderRadiusLG", "lineHeightLG", "colorText", "fontSize", "lineHeight", "fontFamily", "colorTextPlaceholder", "controlHeight", "motionDurationSlow", "colorBorder", "colorBgContainer", "colorBgContainerDisabled", "colorErrorBorderHover", "colorWarning", "colorWarningBorderHover", "colorFillTertiary", "colorFillSecondary", "colorPrimary", "colorErrorBg", "colorErrorBgHover", "colorErrorText", "colorWarningBg", "colorWarningBgHover", "colorWarningText", "paddingXS", "fontSizeLG", "colorSplit"], "component": {"paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7, "paddingInline": 11, "paddingInlineSM": 7, "paddingInlineLG": 11, "addonBg": "rgba(0, 0, 0, 0.02)", "activeBorderColor": "#1677ff", "hoverBorderColor": "#4096ff", "activeShadow": "0 0 0 2px rgba(5, 145, 255, 0.1)", "errorActiveShadow": "0 0 0 2px rgba(255, 38, 5, 0.06)", "warningActiveShadow": "0 0 0 2px rgba(255, 215, 5, 0.1)", "hoverBg": "#ffffff", "activeBg": "#ffffff", "inputFontSize": 14, "inputFontSizeLG": 16, "inputFontSizeSM": 14, "controlWidth": 90, "handleWidth": 22, "handleFontSize": 7, "handleVisible": "auto", "handleActiveBg": "rgba(0, 0, 0, 0.02)", "handleBg": "#ffffff", "filledHandleBg": "#f0f0f0", "handleHoverColor": "#1677ff", "handleBorderColor": "#d9d9d9", "handleOpacity": 0}}, "Input": {"global": ["paddingXXS", "controlHeightSM", "lineWidth", "colorText", "fontSize", "lineHeight", "fontFamily", "borderRadius", "motionDurationMid", "colorTextPlaceholder", "controlHeight", "motionDurationSlow", "lineHeightLG", "borderRadiusLG", "borderRadiusSM", "colorBorder", "colorBgContainer", "lineType", "colorTextDisabled", "colorBgContainerDisabled", "colorError", "colorErrorBorderHover", "colorWarning", "colorWarningBorderHover", "colorFillTertiary", "colorFillSecondary", "colorPrimary", "colorErrorBg", "colorErrorBgHover", "colorErrorText", "colorWarningBg", "colorWarningBgHover", "colorWarningText", "controlHeightLG", "paddingLG", "colorTextDescription", "paddingXS", "colorIcon", "colorIconHover", "colorTextQuaternary", "fontSizeIcon", "colorTextTertiary", "colorSplit", "colorPrimaryHover", "colorPrimaryActive"], "component": {"paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7, "paddingInline": 11, "paddingInlineSM": 7, "paddingInlineLG": 11, "addonBg": "rgba(0, 0, 0, 0.02)", "activeBorderColor": "#1677ff", "hoverBorderColor": "#4096ff", "activeShadow": "0 0 0 2px rgba(5, 145, 255, 0.1)", "errorActiveShadow": "0 0 0 2px rgba(255, 38, 5, 0.06)", "warningActiveShadow": "0 0 0 2px rgba(255, 215, 5, 0.1)", "hoverBg": "#ffffff", "activeBg": "#ffffff", "inputFontSize": 14, "inputFontSizeLG": 16, "inputFontSizeSM": 14}}, "Image": {"global": ["controlHeightLG", "colorBgContainerDisabled", "motionDurationSlow", "paddingXXS", "marginXXS", "colorTextLightSolid", "motionEaseOut", "paddingSM", "marginXL", "margin", "paddingLG", "marginSM", "zIndexPopupBase", "colorBgMask", "motionDurationMid", "motionEaseOutCirc", "motionEaseInOutCirc"], "component": {"zIndexPopup": 1080, "previewOperationColor": "rgba(255, 255, 255, 0.65)", "previewOperationHoverColor": "rgba(255, 255, 255, 0.85)", "previewOperationColorDisabled": "rgba(255, 255, 255, 0.25)", "previewOperationSize": 18}}, "Grid": {"global": [], "component": {}}, "Form": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily", "marginLG", "colorTextDescription", "fontSizeLG", "lineWidth", "lineType", "colorBorder", "controlOutlineWidth", "controlOutline", "paddingSM", "controlHeightSM", "controlHeightLG", "colorError", "colorWarning", "marginXXS", "controlHeight", "motionDurationMid", "motionEaseOut", "motionEaseOutBack", "colorSuccess", "colorPrimary", "motionDurationSlow", "motionEaseInOut", "margin", "screenXSMax", "screenSMMax", "screenMDMax", "screenLGMax"], "component": {"labelRequiredMarkColor": "#ff4d4f", "labelColor": "rgba(0, 0, 0, 0.88)", "labelFontSize": 14, "labelHeight": 32, "labelColonMarginInlineStart": 2, "labelColonMarginInlineEnd": 8, "itemMarginBottom": 24, "verticalLabelPadding": "0 0 8px", "verticalLabelMargin": 0, "inlineItemMarginBottom": 0}}, "FloatButton": {"global": ["colorTextLightSolid", "colorBgElevated", "controlHeightLG", "marginXXL", "marginLG", "fontSize", "fontSizeIcon", "controlItemBgHover", "paddingXXS", "margin", "borderRadiusLG", "borderRadiusSM", "colorText", "lineHeight", "fontFamily", "lineWidth", "lineType", "colorSplit", "boxShadowSecondary", "motionDurationMid", "colorFillContent", "fontSizeLG", "fontSizeSM", "colorPrimary", "colorPrimaryHover", "motionDurationSlow", "motionEaseInOutCirc"], "component": {"dotOffsetInCircle": 5.857864376269049, "dotOffsetInSquare": 2.3431457505076194}}, "Flex": {"global": ["paddingXS", "padding", "paddingLG"], "component": {}}, "Empty": {"global": ["controlHeightLG", "margin", "marginXS", "marginXL", "fontSize", "lineHeight", "opacityImage", "colorTextDescription"], "component": {}}, "Dropdown": {"global": ["marginXXS", "sizePopupArrow", "paddingXXS", "motionDurationMid", "fontSize", "colorTextDisabled", "fontSizeIcon", "controlPaddingHorizontal", "colorBgElevated", "colorText", "lineHeight", "fontFamily", "boxShadowPopoverArrow", "borderRadiusXS", "borderRadiusLG", "boxShadowSecondary", "lineWidthFocus", "colorPrimaryBorder", "colorTextDescription", "marginXS", "fontSizeSM", "borderRadiusSM", "controlItemBgHover", "colorPrimary", "controlItemBgActive", "controlItemBgActiveHover", "colorSplit", "paddingXS", "motionEaseOutQuint", "motionEaseInQuint", "motionEaseOutCirc", "motionEaseInOutCirc", "colorError", "colorTextLightSolid"], "component": {"zIndexPopup": 1050, "paddingBlock": 5, "arrowOffsetHorizontal": 12, "arrowOffsetVertical": 8, "arrowShadowWidth": 8.970562748477143, "arrowPath": "path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z')", "arrowPolygon": "polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%)"}}, "Drawer": {"global": ["borderRadiusSM", "colorBgMask", "colorBgElevated", "motionDurationSlow", "motionDurationMid", "paddingXS", "padding", "paddingLG", "fontSizeLG", "lineHeightLG", "lineWidth", "lineType", "colorSplit", "marginXS", "colorIcon", "colorIconHover", "colorBgTextHover", "colorBgTextActive", "colorText", "fontWeightStrong", "boxShadowDrawerLeft", "boxShadowDrawerRight", "boxShadowDrawerUp", "boxShadowDrawerDown", "lineWidthFocus", "colorPrimaryBorder"], "component": {"zIndexPopup": 1000, "footerPaddingBlock": 8, "footerPaddingInline": 16}}, "Divider": {"global": ["margin", "marginLG", "colorSplit", "lineWidth", "colorText", "fontSize", "lineHeight", "fontFamily", "colorTextHeading", "fontSizeLG"], "component": {"textPaddingInline": "1em", "orientationMargin": 0.05, "verticalMarginInline": 8}}, "Descriptions": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily", "lineWidth", "lineType", "colorSplit", "padding", "paddingLG", "colorTextSecondary", "paddingSM", "paddingXS", "fontWeightStrong", "fontSizeLG", "lineHeightLG", "borderRadiusLG", "colorTextTertiary"], "component": {"labelBg": "rgba(0, 0, 0, 0.02)", "titleColor": "rgba(0, 0, 0, 0.88)", "titleMarginBottom": 20, "itemPaddingBottom": 16, "colonMarginRight": 8, "colonMarginLeft": 2, "contentColor": "rgba(0, 0, 0, 0.88)", "extraColor": "rgba(0, 0, 0, 0.88)"}}, "DatePicker": {"global": ["paddingXXS", "controlHeightLG", "padding", "paddingSM", "controlHeight", "lineWidth", "colorPrimary", "colorPrimaryBorder", "lineType", "colorSplit", "colorTextDisabled", "colorBorder", "borderRadius", "motionDurationMid", "colorTextPlaceholder", "fontSizeLG", "controlHeightSM", "paddingXS", "marginXS", "colorTextDescription", "lineWidthBold", "motionDurationSlow", "sizePopupArrow", "colorBgElevated", "borderRadiusLG", "boxShadowSecondary", "borderRadiusSM", "boxShadowPopoverArrow", "fontHeight", "fontHeightLG", "lineHeightLG", "colorText", "fontSize", "lineHeight", "fontFamily", "colorBgContainer", "colorTextHeading", "colorIcon", "colorIconHover", "fontWeightStrong", "colorTextLightSolid", "controlItemBgActive", "marginXXS", "colorFillSecondary", "colorTextTertiary", "borderRadiusXS", "motionEaseOutQuint", "motionEaseInQuint", "motionEaseOutCirc", "motionEaseInOutCirc", "colorBgContainerDisabled", "colorError", "colorErrorBorderHover", "colorWarning", "colorWarningBorderHover", "colorFillTertiary", "colorErrorBg", "colorErrorBgHover", "colorErrorText", "colorWarningBg", "colorWarningBgHover", "colorWarningText"], "component": {"paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7, "paddingInline": 11, "paddingInlineSM": 7, "paddingInlineLG": 11, "addonBg": "rgba(0, 0, 0, 0.02)", "activeBorderColor": "#1677ff", "hoverBorderColor": "#4096ff", "activeShadow": "0 0 0 2px rgba(5, 145, 255, 0.1)", "errorActiveShadow": "0 0 0 2px rgba(255, 38, 5, 0.06)", "warningActiveShadow": "0 0 0 2px rgba(255, 215, 5, 0.1)", "hoverBg": "#ffffff", "activeBg": "#ffffff", "inputFontSize": 14, "inputFontSizeLG": 16, "inputFontSizeSM": 14, "INTERNAL_FIXED_ITEM_MARGIN": 2, "cellHoverBg": "rgba(0, 0, 0, 0.04)", "cellActiveWithRangeBg": "#e6f4ff", "cellHoverWithRangeBg": "#c8dfff", "cellRangeBorderColor": "#7cb3ff", "cellBgDisabled": "rgba(0, 0, 0, 0.04)", "timeColumnWidth": 56, "timeColumnHeight": 224, "timeCellHeight": 28, "cellWidth": 36, "cellHeight": 24, "textHeight": 40, "withoutTimeCellHeight": 66, "multipleItemBg": "rgba(0, 0, 0, 0.06)", "multipleItemBorderColor": "transparent", "multipleItemHeight": 24, "multipleItemHeightSM": 16, "multipleItemHeightLG": 32, "multipleSelectorBgDisabled": "rgba(0, 0, 0, 0.04)", "multipleItemColorDisabled": "rgba(0, 0, 0, 0.25)", "multipleItemBorderColorDisabled": "transparent", "arrowShadowWidth": 8.970562748477143, "arrowPath": "path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z')", "arrowPolygon": "polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%)", "presetsWidth": 120, "presetsMaxWidth": 200, "zIndexPopup": 1050}}, "ColorPicker": {"global": ["colorTextQuaternary", "marginSM", "colorPrimary", "motionDurationMid", "colorBgElevated", "colorTextDisabled", "colorText", "colorBgContainerDisabled", "borderRadius", "marginXS", "controlHeight", "controlHeightSM", "colorBgTextActive", "lineWidth", "colorBorder", "paddingXXS", "fontSize", "colorPrimaryHover", "controlOutline", "controlHeightLG", "borderRadiusSM", "colorFillSecondary", "lineWidthBold", "fontSizeSM", "lineHeightSM", "marginXXS", "fontSizeIcon", "paddingXS", "colorTextPlaceholder", "colorFill", "colorWhite", "fontHeightSM", "motionEaseInBack", "motionDurationFast", "motionEaseOutBack", "colorSplit", "red6", "controlOutlineWidth", "colorError", "colorWarning", "colorErrorHover", "colorWarningHover", "colorErrorOutline", "colorWarningOutline", "controlHeightXS", "borderRadiusXS", "borderRadiusLG", "fontSizeLG"], "component": {}}, "Collapse": {"global": ["paddingXS", "paddingSM", "padding", "paddingLG", "borderRadiusLG", "lineWidth", "lineType", "colorBorder", "colorText", "colorTextHeading", "colorTextDisabled", "fontSizeLG", "lineHeight", "lineHeightLG", "marginSM", "motionDurationSlow", "fontSizeIcon", "fontHeight", "fontHeightLG", "fontSize", "fontFamily", "paddingXXS", "motionDurationMid", "motionEaseInOut"], "component": {"headerPadding": "12px 16px", "headerBg": "rgba(0, 0, 0, 0.02)", "contentPadding": "16px 16px", "contentBg": "#ffffff"}}, "Checkbox": {"global": ["controlInteractiveSize", "colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "borderRadiusSM", "lineWidthFocus", "colorPrimaryBorder", "colorBgContainer", "lineWidth", "lineType", "colorBorder", "motionDurationSlow", "lineWidthBold", "colorWhite", "motionDurationFast", "motionEaseInBack", "paddingXS", "colorPrimary", "colorPrimaryHover", "motionDurationMid", "motionEaseOutBack", "fontSizeLG", "colorBgContainerDisabled", "colorTextDisabled"], "component": {}}, "Cascader": {"global": ["controlInteractiveSize", "colorText", "fontSize", "lineHeight", "fontFamily", "marginXS", "borderRadiusSM", "lineWidthFocus", "colorPrimaryBorder", "colorBgContainer", "lineWidth", "lineType", "colorBorder", "motionDurationSlow", "lineWidthBold", "colorWhite", "motionDurationFast", "motionEaseInBack", "paddingXS", "colorPrimary", "colorPrimaryHover", "motionDurationMid", "motionEaseOutBack", "fontSizeLG", "colorBgContainerDisabled", "colorTextDisabled", "colorSplit", "controlItemBgHover", "paddingXXS", "colorTextDescription", "fontSizeIcon", "colorHighlight"], "component": {"controlWidth": 184, "controlItemWidth": 111, "dropdownHeight": 180, "optionSelectedBg": "#e6f4ff", "optionSelectedFontWeight": 600, "optionPadding": "5px 12px", "menuPadding": 4}}, "Carousel": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily", "motionDurationSlow", "colorBgContainer", "marginXXS"], "component": {"arrowSize": 16, "arrowOffset": 8, "dotWidth": 16, "dotHeight": 3, "dotGap": 4, "dotOffset": 12, "dotWidthActive": 24, "dotActiveWidth": 24}}, "Card": {"global": ["boxShadowCard", "padding", "paddingLG", "fontSize", "colorBorderSecondary", "boxShadowTertiary", "colorText", "lineHeight", "fontFamily", "colorBgContainer", "borderRadiusLG", "colorTextHeading", "fontWeightStrong", "lineWidth", "lineType", "motionDurationMid", "colorTextDescription", "colorPrimary", "fontHeight", "marginXXS", "marginXS", "fontSizeLG", "colorFillAlter"], "component": {"headerBg": "transparent", "headerFontSize": 16, "headerFontSizeSM": 14, "headerHeight": 56, "headerHeightSM": 38, "actionsBg": "#ffffff", "actionsLiMargin": "12px 0", "tabsMarginBottom": -17, "extraColor": "rgba(0, 0, 0, 0.88)"}}, "Calendar": {"global": ["controlHeightLG", "paddingXXS", "padding", "controlHeightSM", "fontHeightSM", "marginXS", "lineWidth", "paddingSM", "paddingXS", "colorBgContainer", "lineType", "borderRadiusLG", "colorPrimary", "colorTextHeading", "colorSplit", "colorIcon", "motionDurationMid", "colorIconHover", "fontWeightStrong", "colorTextDisabled", "colorText", "fontSize", "motionDurationSlow", "borderRadiusSM", "colorTextLightSolid", "controlItemBgActive", "marginXXS", "colorFillSecondary", "colorTextTertiary", "lineHeight", "fontFamily", "controlItemBgHover", "lineWidthBold", "screenXS"], "component": {"fullBg": "#ffffff", "fullPanelBg": "#ffffff", "itemActiveBg": "#e6f4ff", "yearControlWidth": 80, "monthControlWidth": 70, "miniContentHeight": 256, "INTERNAL_FIXED_ITEM_MARGIN": 2, "cellHoverBg": "rgba(0, 0, 0, 0.04)", "cellActiveWithRangeBg": "#e6f4ff", "cellHoverWithRangeBg": "#c8dfff", "cellRangeBorderColor": "#7cb3ff", "cellBgDisabled": "rgba(0, 0, 0, 0.04)", "timeColumnWidth": 56, "timeColumnHeight": 224, "timeCellHeight": 28, "cellWidth": 36, "cellHeight": 24, "textHeight": 40, "withoutTimeCellHeight": 66, "multipleItemBg": "rgba(0, 0, 0, 0.06)", "multipleItemBorderColor": "transparent", "multipleItemHeight": 24, "multipleItemHeightSM": 16, "multipleItemHeightLG": 32, "multipleSelectorBgDisabled": "rgba(0, 0, 0, 0.04)", "multipleItemColorDisabled": "rgba(0, 0, 0, 0.25)", "multipleItemBorderColorDisabled": "transparent"}}, "Button": {"global": ["marginXS", "lineWidth", "lineType", "motionDurationMid", "motionEaseInOut", "colorText", "lineWidthFocus", "colorPrimaryBorder", "controlHeight", "borderRadius", "opacityLoading", "motionDurationSlow", "controlHeightSM", "paddingXS", "borderRadiusSM", "controlHeightLG", "borderRadiusLG", "colorTextDisabled", "colorBgContainerDisabled", "colorBorder", "colorError", "colorErrorHover", "colorErrorBorderHover", "colorErrorActive", "colorPrimary", "colorTextLightSolid", "colorPrimaryHover", "colorPrimaryActive", "colorLink", "colorLinkHover", "colorLinkActive", "colorBgTextActive", "colorErrorBg", "colorErrorBgActive", "colorBgContainer", "fontSize"], "component": {"fontWeight": 400, "defaultShadow": "0 2px 0 rgba(0, 0, 0, 0.02)", "primaryShadow": "0 2px 0 rgba(5, 145, 255, 0.1)", "dangerShadow": "0 2px 0 rgba(255, 38, 5, 0.06)", "primaryColor": "#fff", "dangerColor": "#fff", "borderColorDisabled": "#d9d9d9", "defaultGhostColor": "#ffffff", "ghostBg": "transparent", "defaultGhostBorderColor": "#ffffff", "paddingInline": 15, "paddingInlineLG": 15, "paddingInlineSM": 7, "onlyIconSize": 16, "onlyIconSizeSM": 14, "onlyIconSizeLG": 18, "groupBorderColor": "#4096ff", "linkHoverBg": "transparent", "textHoverBg": "rgba(0, 0, 0, 0.06)", "defaultColor": "rgba(0, 0, 0, 0.88)", "defaultBg": "#ffffff", "defaultBorderColor": "#d9d9d9", "defaultBorderColorDisabled": "#d9d9d9", "defaultHoverBg": "#ffffff", "defaultHoverColor": "#4096ff", "defaultHoverBorderColor": "#4096ff", "defaultActiveBg": "#ffffff", "defaultActiveColor": "#0958d9", "defaultActiveBorderColor": "#0958d9", "contentFontSize": 14, "contentFontSizeSM": 14, "contentFontSizeLG": 16, "contentLineHeight": 1.5714285714285714, "contentLineHeightSM": 1.5714285714285714, "contentLineHeightLG": 1.5, "paddingBlock": 4, "paddingBlockSM": 0, "paddingBlockLG": 7}}, "Breadcrumb": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily", "motionDurationMid", "paddingXXS", "borderRadiusSM", "fontHeight", "marginXXS", "colorBgTextHover", "lineWidthFocus", "colorPrimaryBorder", "fontSizeIcon"], "component": {"itemColor": "rgba(0, 0, 0, 0.45)", "lastItemColor": "rgba(0, 0, 0, 0.88)", "iconFontSize": 14, "linkColor": "rgba(0, 0, 0, 0.45)", "linkHoverColor": "rgba(0, 0, 0, 0.88)", "separatorColor": "rgba(0, 0, 0, 0.45)", "separatorMargin": 8}}, "Badge": {"global": ["fontHeight", "lineWidth", "marginXS", "colorBorderBg", "colorBgContainer", "colorError", "colorErrorHover", "motionDurationSlow", "blue1", "blue3", "blue6", "blue7", "purple1", "purple3", "purple6", "purple7", "cyan1", "cyan3", "cyan6", "cyan7", "green1", "green3", "green6", "green7", "magenta1", "magenta3", "magenta6", "magenta7", "pink1", "pink3", "pink6", "pink7", "red1", "red3", "red6", "red7", "orange1", "orange3", "orange6", "orange7", "yellow1", "yellow3", "yellow6", "yellow7", "volcano1", "volcano3", "volcano6", "volcano7", "geekblue1", "geekblue3", "geekblue6", "geekblue7", "lime1", "lime3", "lime6", "lime7", "gold1", "gold3", "gold6", "gold7", "colorText", "fontSize", "lineHeight", "fontFamily", "motionDurationMid", "paddingXS", "colorSuccess", "colorInfo", "colorTextPlaceholder", "colorWarning", "motionEaseOutBack"], "component": {"indicatorZIndex": "auto", "indicatorHeight": 20, "indicatorHeightSM": 14, "dotSize": 6, "textFontSize": 12, "textFontSizeSM": 12, "textFontWeight": "normal", "statusSize": 6}}, "BackTop": {"global": ["fontSizeHeading3", "colorTextDescription", "colorTextLightSolid", "colorText", "controlHeightLG", "fontSize", "lineHeight", "fontFamily", "motionDurationMid", "screenMD", "screenXS"], "component": {"zIndexPopup": 10}}, "Avatar": {"global": ["colorTextLightSolid", "colorTextPlaceholder", "borderRadius", "borderRadiusLG", "borderRadiusSM", "lineWidth", "lineType", "colorText", "fontSize", "lineHeight", "fontFamily"], "component": {"containerSize": 32, "containerSizeLG": 40, "containerSizeSM": 24, "textFontSize": 18, "textFontSizeLG": 24, "textFontSizeSM": 14, "groupSpace": 4, "groupOverlapping": -8, "groupBorderColor": "#ffffff"}}, "App": {"global": ["colorText", "fontSize", "lineHeight", "fontFamily"], "component": {}}, "Anchor": {"global": ["fontSize", "fontSizeLG", "paddingXXS", "motionDurationSlow", "lineWidthBold", "colorPrimary", "lineType", "colorSplit", "colorText", "lineHeight", "fontFamily", "lineWidth"], "component": {"linkPaddingBlock": 4, "linkPaddingInlineStart": 16}}, "Alert": {"global": ["motionDurationSlow", "marginXS", "marginSM", "fontSize", "fontSizeLG", "lineHeight", "borderRadiusLG", "motionEaseInOutCirc", "colorText", "colorTextHeading", "fontFamily", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "lineWidth", "lineType", "motionDurationMid", "fontSizeIcon", "colorIcon", "colorIconHover"], "component": {"withDescriptionIconSize": 24, "defaultPadding": "8px 12px", "withDescriptionPadding": "20px 24px"}}, "Affix": {"global": [], "component": {"zIndexPopup": 10}}}