<svg width="30" height="30" viewBox="0 0 30 30" fill="none"
     xmlns="http://www.w3.org/2000/svg">
  <rect width="30" height="30" rx="15" fill="#406CF6"/>
  <g filter="url(#filter0_d_582_251)">
    <rect x="9.1665" y="17.4205" width="3.9644" height="9.5" rx="1.9822"
          transform="rotate(-90 9.1665 17.4205)" fill="white"/>
  </g>
  <g filter="url(#filter1_d_582_251)">
    <rect x="9.1665" y="10.9644" width="3.9644" height="11.6667" rx="1.9822"
          transform="rotate(-90 9.1665 10.9644)" fill="white"/>
  </g>
  <rect x="9.1665" y="7.0946" width="3.98831" height="11.2325" rx="1.99415"
        fill="white"/>
  <g filter="url(#filter2_d_582_251)">
    <ellipse cx="11.2033" cy="21.1004" rx="1.91667" ry="1.89961" fill="white"/>
  </g>
  <defs>
    <filter id="filter0_d_582_251" x="7.1665" y="11.4562" width="17.5"
            height="11.9644" filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                     result="hardAlpha"/>
      <feOffset dx="2" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix"
               result="effect1_dropShadow_582_251"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_582_251"
               result="shape"/>
    </filter>
    <filter id="filter1_d_582_251" x="7.1665" y="5" width="19.6665"
            height="11.9644" filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                     result="hardAlpha"/>
      <feOffset dx="2" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix"
               result="effect1_dropShadow_582_251"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_582_251"
               result="shape"/>
    </filter>
    <filter id="filter2_d_582_251" x="7.28662" y="17.2008" width="11.8335"
            height="11.7992" filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                     result="hardAlpha"/>
      <feOffset dx="2" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix"
                     values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix"
               result="effect1_dropShadow_582_251"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_582_251"
               result="shape"/>
    </filter>
  </defs>
</svg>
