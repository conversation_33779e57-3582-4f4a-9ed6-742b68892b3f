import { Mode } from '../interface/enum';
import { SDK_HOST } from '../utils/constants';
import log from '../utils/prettyLog';

export default class Initialization {
  private options!: InitSDKOptions;

  // 创建 script 标签的方法
  createScriptElement(id: string, src: string) {
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;
    script.id = id;
    script.src = src;
    return script;
  }

  // 加载脚本的方法
  loadScript() {
    const { mode = Mode.EDITOR } = this.options;
    const scriptId = mode === Mode.EDITOR ? 'flowez-editing' : 'flowez-main';
    const scriptSrc = `${SDK_HOST}/${mode === Mode.EDITOR ? 'Editor.js' : 'Main.js'}`;

    const scriptEle = this.createScriptElement(scriptId, scriptSrc);

    scriptEle.onload = () => {
      log.success('FLOW-EZ', `${scriptId} loaded successfully.`);
      if (mode === Mode.EDITOR) {
        const editor = new window.FlowEZ.Editor();
        editor.init(this.options);
      } else {
        const main = new window.FlowEZ.Main();
        main.init(this.options);
      }
    };
    scriptEle.onerror = () => log.error('FLOW-EZ', `${scriptId} failed to load`);

    const location: HTMLScriptElement = document.getElementsByTagName('script')[0];
    location?.parentNode?.insertBefore(scriptEle, location);
  }

  // 初始化方法
  init(options: InitSDKOptions) {
    if (typeof options === 'object' && typeof this.options === 'object') {
      this.options = Object.assign(this.options, options);
    } else {
      this.options = options;
    }
    if (!this.options.companyId || !this.options.applicationId) {
      log.error('FLOW-EZ', 'The current SDK is not registered. Please contact the administrator to register the SDK.');
    } else {
      this.loadScript();
    }
  }
}
