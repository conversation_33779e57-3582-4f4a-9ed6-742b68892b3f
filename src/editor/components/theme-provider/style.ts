import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle<{ $maxZIndex: number }>`
  /* 全局样式 */
  :host {
    position: absolute;
    z-index: ${(props) => props.$maxZIndex};

    /* 自定义滚动条样式 */ /* WebKit-based browsers */
    ::-webkit-scrollbar {
      width: 12px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    /* Firefox */
    * {
      scrollbar-width: thin;
      scrollbar-color: #888 #f1f1f1;
    }

    /* Edge and IE */
    * {
      -ms-overflow-style: -ms-autohiding-scrollbar;
    }
  }
`;
