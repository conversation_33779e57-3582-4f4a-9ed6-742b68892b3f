import React, { useEffect } from 'react';

import { ConfigProvider } from '@/components';
import useGlobalStore from '@/stores/global';

import EditorRoot from '../editor-root';
import PortalRoot from '../portal-root';
import ThemeProvider from '../theme-provider';
import { getBuilderRoot, getEditorRoot, getPortalRoot } from '../utils/document';

interface Props {
  options: InitSDKOptions;
}

const BuilderRoot = (props: Props) => {
  const { options } = props;
  const { setInitSDKOptions } = useGlobalStore();
  const $builderRoot = getBuilderRoot();

  useEffect(() => {
    setInitSDKOptions(options);
  }, [options, setInitSDKOptions]);

  return (
    <ThemeProvider target={$builderRoot}>
      <ConfigProvider
        getPopupContainer={(triggerNode) => {
          const $editorRoot = getEditorRoot() as HTMLElement;
          if ($editorRoot) {
            return $editorRoot;
          }
          return triggerNode?.parentElement || document.body;
        }}
      >
        <div id='editor-root'>
          <EditorRoot />
        </div>
      </ConfigProvider>
      <ConfigProvider
        getPopupContainer={(triggerNode) => {
          const $portalRoot = getPortalRoot() as HTMLElement;
          if ($portalRoot) {
            return $portalRoot;
          }
          return triggerNode?.parentElement || document.body;
        }}
        theme={{
          components: {
            Popover: {
              colorBgElevated: '#fff',
            },
          },
        }}
      >
        <div id='portal-root'>
          <PortalRoot />
        </div>
      </ConfigProvider>
    </ThemeProvider>
  );
};

export default BuilderRoot;
