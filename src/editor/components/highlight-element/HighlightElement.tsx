import React, { useEffect, useRef, useCallback } from 'react';
import { nanoid } from 'nanoid';
import dayjs from 'dayjs';
import uniqueSelector from 'unique-selector';

import { ConfigProvider, Popover } from '@/components';
import useEditorStore from '../../stores';
import TooltipTemplate from '../template/tooltip';
import { checkIfExcludedElement, filterSvgElement } from '../utils/document';
import { OperationEnum } from '../utils/enum';
import { Highlight, Key, Span, FloatTips } from './styles';
import { initialStep } from '@/utils/constants';

// 抽取类型定义
interface Position {
  left: number;
  top: number;
  width: number;
  height: number;
}

const HighlightElement: React.FC = () => {
  const { setType, stepType, setStepType, step, updateStep, setStep, steps, addStep } = useEditorStore();
  const highlightRef = useRef<HTMLDivElement>(null);

  // 优化键盘事件处理
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      setType(OperationEnum.EDIT);
      setStepType(undefined);
    }
  }, []);

  // 优化元素位置更新
  const updateElementPosition = useCallback((position: Position) => {
    if (!highlightRef.current) return;

    requestAnimationFrame(() => {
      const element = highlightRef.current;
      if (!element) return;

      element.style.left = `${position.left - 5}px`;
      element.style.top = `${position.top - 5}px`;
      element.style.width = `${position.width + 10}px`;
      element.style.height = `${position.height + 10}px`;
    });
  }, []);

  // 优化鼠标移动事件处理
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (checkIfExcludedElement(e) || !e.target || !(e.target as Element).isConnected) return;

    const target = filterSvgElement(e.target as Element);
    let current = target;

    while (current && typeof current.className !== 'string') {
      current = current.parentElement;
      if (!current) return;
    }

    const boundingRect = current.getBoundingClientRect();
    updateElementPosition({
      left: boundingRect.left,
      top: boundingRect.top,
      width: boundingRect.width,
      height: boundingRect.height,
    });
  }, []);

  // 优化点击事件处理
  const handleClick = useCallback((e: MouseEvent) => {
    if (checkIfExcludedElement(e)) return;

    e.stopPropagation();
    e.preventDefault();

    const selector = uniqueSelector(e.target as Element);
    if (!selector) return;

    if (step?.stepId) {
      updateStep({
        ...step,
        location: selector,
        customLocation: selector,
      });
    } else {
      const orderSeq = steps.length + 1;
      const newStep = {
        ...initialStep,
        stepId: nanoid(),
        stepType: stepType!,
        orderSeq,
        location: selector,
        customLocation: selector,
        createionDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };
      setStep(newStep);
      addStep(newStep);
    }
    setType(OperationEnum.EDIT);
  }, []);

  // 优化事件监听
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('click', handleClick);
    window.addEventListener('pointerdown', handleClick);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('click', handleClick);
      window.removeEventListener('pointerdown', handleClick);
    };
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Popover: {
            colorBgElevated: '#fff',
          },
        },
      }}
    >
      <Popover overlayInnerStyle={{ padding: 0 }} content={<TooltipTemplate />} open>
        <Highlight ref={highlightRef} />
      </Popover>
      <FloatTips>
        <Span>
          <Key>Hold Shift &nbsp;</Key>
          to interact with the page
        </Span>
        <Span>
          <Key>Esc &nbsp;</Key>
          to cancel
        </Span>
      </FloatTips>
    </ConfigProvider>
  );
};

export default React.memo(HighlightElement);
