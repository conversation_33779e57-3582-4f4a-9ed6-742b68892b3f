import React, { useState, useCallback } from 'react';
import { nanoid } from 'nanoid';
import {
  DndContext,
  useSensor,
  MouseSensor,
  TouchSensor,
  DragOverlay,
  DropAnimation,
  MeasuringStrategy,
  useSensors,
  Active,
  Over,
} from '@dnd-kit/core';
import type { DragEndEvent, UniqueIdentifier, MeasuringConfiguration } from '@dnd-kit/core';
import { restrictToVerticalAxis, restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { verticalListSortingStrategy, SortableContext } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { TooltipContainer } from './style';
import AddRow from './AddRow';
import DraggableRow, { RowOverly } from './DraggableRow';
import { ElementOverly } from './DraggableElement';
import { TooltipTemplateProps, RowType, ElementType } from './interface';

// 抽取拖拽相关配置
const DND_SENSOR_CONFIG = {
  activationConstraint: {
    distance: 2,
  },
};

// 默认行数据
const defaultRows: RowType[] = [
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: 'Track your favorites',
      },
    ],
  },
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: 'Anything you favorite will be stored here for safekeeping.',
      },
    ],
  },
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: '⊘ Hide these tips',
      },
      {
        id: nanoid(),
        type: 'Button',
        content: '按钮',
      },
    ],
  },
];

const measuring: MeasuringConfiguration = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
};

const dropAnimation: DropAnimation = {
  keyframes({ transform }) {
    return [
      { transform: CSS.Transform.toString(transform.initial) },
      {
        transform: CSS.Transform.toString({
          scaleX: 0.98,
          scaleY: 0.98,
          x: transform.final.x - 10,
          y: transform.final.y - 10,
        }),
      },
    ];
  },
};

export const TooltipTemplate: React.FC<TooltipTemplateProps> = ({
  initialRows = defaultRows,
  containerStyle,
  showAddRowButton = true,
  enableDragging = true,
  enableHoverEffect = true,
  availableElementTypes = ['Text', 'Button', 'Image', 'Link'],
  onRowsChange,
  onElementAdd,
  onElementRemove,
  onRowAdd,
  onRowRemove,
}) => {
  const [rows, setRows] = useState<RowType[]>(initialRows);
  const [isHovering, setIsHovering] = useState(false);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [activeType, setActiveType] = useState<'row' | 'element' | null>(null);

  const mouseSensor = useSensor(MouseSensor, DND_SENSOR_CONFIG);
  const touchSensor = useSensor(TouchSensor, DND_SENSOR_CONFIG);
  const sensors = useSensors(mouseSensor, touchSensor);

  const activeIndex = useCallback(() => {
    if (activeType !== null && activeId !== null) {
      if (activeType === 'row') {
        return rows.map((row: any) => row.id).indexOf(activeId);
      } else {
        return rows.flatMap((row) => row.elements).findIndex((el) => el.id === activeId);
      }
    }
    return -1;
  }, [activeType, rows, activeId]);

  const handleDragStart = ({ active }: DragEndEvent) => {
    if (!enableDragging) return;

    const activeRow = rows.find((row) => row.id === active.id);
    if (activeRow) {
      setIsHovering(false);
      setActiveType('row');
    } else {
      setActiveType('element');
    }
    setActiveId(active.id);
  };

  const handleDragCancel = () => {
    setActiveId(null);
    setActiveType(null);
  };

  const handleRowDragEnd = (active: Active, over: Over) => {
    if (!active || !over || active.id === over.id) return;

    const oldIndex = rows.findIndex((row) => row.id === active.id);
    const newIndex = rows.findIndex((row) => row.id === over.id);

    setRows((prevRows) => {
      const newRows = [...prevRows];
      const [removedRow] = newRows.splice(oldIndex, 1);
      newRows.splice(newIndex, 0, removedRow);

      // 调用回调
      onRowsChange?.(newRows);

      return newRows;
    });
  };

  const handleElementDragEnd = (active: Active, over: Over) => {
    if (!active || !over) return;

    // 找到源元素和目标元素所在的行
    let sourceRowIndex = -1;
    let sourceElementIndex = -1;
    let targetRowIndex = -1;
    let targetElementIndex = -1;

    // 查找源元素位置
    rows.forEach((row, rowIndex) => {
      const elementIndex = row.elements.findIndex((el) => el.id === active.id);
      if (elementIndex !== -1) {
        sourceRowIndex = rowIndex;
        sourceElementIndex = elementIndex;
      }
    });

    // 如果是拖到了行上
    if (rows.some((row) => row.id === over.id)) {
      targetRowIndex = rows.findIndex((row) => row.id === over.id);
      targetElementIndex = rows[targetRowIndex].elements.length; // 放到行末尾
    } else {
      // 查找目标元素位置
      rows.forEach((row, rowIndex) => {
        const elementIndex = row.elements.findIndex((el) => el.id === over.id);
        if (elementIndex !== -1) {
          targetRowIndex = rowIndex;
          targetElementIndex = elementIndex;
        }
      });
    }

    if (sourceRowIndex === -1 || targetRowIndex === -1) return;

    setRows((prevRows) => {
      const newRows = [...prevRows];
      const sourceRow = [...newRows[sourceRowIndex].elements];
      const [movedElement] = sourceRow.splice(sourceElementIndex, 1);

      // 如果是同一行
      if (sourceRowIndex === targetRowIndex) {
        sourceRow.splice(targetElementIndex, 0, movedElement);
        newRows[sourceRowIndex] = {
          ...newRows[sourceRowIndex],
          elements: sourceRow,
        };
      } else {
        // 跨行拖拽
        const targetRow = [...newRows[targetRowIndex].elements];
        targetRow.splice(targetElementIndex, 0, movedElement);

        newRows[sourceRowIndex] = {
          ...newRows[sourceRowIndex],
          elements: sourceRow,
        };
        newRows[targetRowIndex] = {
          ...newRows[targetRowIndex],
          elements: targetRow,
        };
      }

      // 如果源行没有元素了，删除该行
      if (sourceRow.length === 0) {
        newRows.splice(sourceRowIndex, 1);
        onRowRemove?.(newRows[sourceRowIndex].id);
      }

      // 调用回调
      onRowsChange?.(newRows);
      onElementAdd?.(movedElement, newRows[targetRowIndex].id);

      return newRows;
    });
  };

  const handleDragEnd = ({ active, over }: DragEndEvent) => {
    if (!enableDragging || !active || !over || active.id === over.id) return;

    if (activeType === 'row') {
      handleRowDragEnd(active, over);
    } else {
      handleElementDragEnd(active, over);
    }

    setActiveId(null);
    setActiveType(null);
  };

  const handleAddRow = (newRow: RowType) => {
    setRows((prevRows) => {
      const newRows = [...prevRows, newRow];
      onRowsChange?.(newRows);
      onRowAdd?.(newRow);
      return newRows;
    });
  };

  const handleMouseEnter = () => {
    if (enableHoverEffect) {
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (enableHoverEffect) {
      setIsHovering(false);
    }
  };

  return (
    <TooltipContainer style={containerStyle} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <DndContext
        sensors={sensors}
        modifiers={activeType === 'row' ? [restrictToVerticalAxis] : undefined}
        measuring={measuring}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        {showAddRowButton && <AddRow isHovering={isHovering} top onAddRow={handleAddRow} />}
        <SortableContext items={rows.map((row) => row.id)} strategy={verticalListSortingStrategy}>
          {rows.map((row, index) => (
            <DraggableRow
              key={row.id}
              activeId={activeId}
              row={row}
              index={index}
              activeIndex={activeIndex()}
              activeType={activeType}
              rows={rows}
              enableDragging={enableDragging}
              availableElementTypes={availableElementTypes}
              onElementRemove={(elementId: string) => onElementRemove?.(elementId, row.id)}
            />
          ))}
        </SortableContext>
        {showAddRowButton && <AddRow isHovering={isHovering} onAddRow={handleAddRow} />}
        <DragOverlay dropAnimation={dropAnimation}>
          {activeId != null && activeType === 'row' ? (
            <RowOverly activeId={activeId} rows={rows} />
          ) : activeId != null && activeType === 'element' ? (
            <ElementOverly
              element={rows.flatMap((row) => row.elements).find((el) => el.id === activeId)}
              activeId={activeId}
              rows={rows}
            />
          ) : null}
        </DragOverlay>
      </DndContext>
    </TooltipContainer>
  );
};
