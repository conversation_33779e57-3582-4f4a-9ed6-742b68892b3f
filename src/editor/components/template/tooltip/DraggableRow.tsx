import React, { useState } from 'react';
import { HolderOutlined } from '@ant-design/icons';
import { useDndContext } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS, isKeyboardEvent } from '@dnd-kit/utilities';
import { SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import type { UniqueIdentifier } from '@dnd-kit/core';

import { RowContainer, RowDragIcon, RowContent } from './style';
import { Position, RowType } from './interface';
import DraggableElement from './DraggableElement';
import AddRow from './AddRow';

interface RowOverlyProps {
  activeId: UniqueIdentifier;
  rows: RowType[];
}

export const RowOverly: React.FC<RowOverlyProps> = ({ activeId, rows }) => {
  const { activatorEvent, over } = useDndContext();
  const isKeyboardSorting = isKeyboardEvent(activatorEvent);
  const activeIndex = rows.map((row) => row.id).indexOf(activeId as string);
  const overIndex = over?.id ? rows.map((row) => row.id).indexOf(over?.id as string) : -1;

  const activeRow = rows.find((row) => row.id === activeId);
  if (!activeRow) return null;

  return (
    <DraggableRow
      row={activeRow}
      isOverly
      rows={rows}
      insertPosition={
        isKeyboardSorting && overIndex !== activeIndex
          ? overIndex > activeIndex
            ? Position.After
            : Position.Before
          : undefined
      }
    />
  );
};

interface DraggableRowProps {
  row: RowType;
  isOverly?: boolean;
  insertPosition?: Position;
  index?: number;
  activeIndex?: number;
  activeType?: 'row' | 'element' | null;
  rows: RowType[];
  activeId?: UniqueIdentifier | null;
  enableDragging?: boolean;
  availableElementTypes?: ('Text' | 'Button' | 'Image' | 'Link')[];
  onElementRemove?: (elementId: string) => void;
}

const DraggableRow: React.FC<DraggableRowProps> = ({
  row,
  isOverly = false,
  insertPosition,
  index = 0,
  activeIndex = -1,
  activeType,
  rows = [],
  activeId = null,
  enableDragging = false,
  availableElementTypes = ['Text', 'Button', 'Image', 'Link'],
  onElementRemove,
}) => {
  const [isHovering, setIsHovering] = useState(false);

  const { isDragging, isSorting, over, setNodeRef, attributes, listeners, transform, transition } = useSortable({
    id: row?.id,
    data: {
      index,
      ...row,
    },
    transition: {
      duration: 500,
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
    disabled: !enableDragging,
  });

  const position = isOverly
    ? insertPosition
    : over?.id === row.id
      ? index > activeIndex
        ? Position.After
        : Position.Before
      : undefined;

  const styles = {
    transform: isSorting ? undefined : CSS.Translate.toString(transform),
    transition,
  };

  return (
    <RowContainer
      ref={setNodeRef}
      {...attributes}
      style={styles}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      $isHover={isHovering}
      $isDragging={isDragging}
      $insertPosition={position}
    >
      <RowDragIcon {...(activeType === 'element' ? {} : listeners)} $isHover={isHovering}>
        <HolderOutlined />
      </RowDragIcon>
      <RowContent $insertPosition={position} $isDragging={isDragging} $isClone={isOverly} $isRow={activeType === 'row'}>
        <SortableContext
          items={row.elements.map((element: any) => element.id)}
          strategy={horizontalListSortingStrategy}
        >
          {row.elements.map((element: any) => (
            <DraggableElement key={element.id} element={element} activeIndex={activeIndex} rows={rows} />
          ))}
        </SortableContext>
      </RowContent>
      <AddRow isHovering={isHovering} isElement />
    </RowContainer>
  );
};

export default DraggableRow;
