import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface State {
  initSDKOptions: InitSDKOptions;
  [key: string]: any;
}

interface Actions {
  setInitSDKOptions: (initSDKOptions: InitSDKOptions) => void;
  getInitSDKOptions: () => InitSDKOptions;
}

const useGlobalStore = create<State & Actions>()(
  devtools((set, get) => ({
    initSDKOptions: {} as InitSDKOptions,
    root: undefined,
    setInitSDKOptions: (initSDKOptions: InitSDKOptions) => set({ initSDKOptions }),
    getInitSDKOptions: () => get().initSDKOptions,
  }))
);

export default useGlobalStore;
